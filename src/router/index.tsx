import { lazy } from 'react';
import { Navigate, create<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RouterProvider, RouteObject } from 'react-router-dom';
import '../assets/css/common.scss';
import Record from '@/pages/Transfer/Record/Record';
import FAQ from '../pages/FAQ/FAQ';

const Home = lazy(() => import('../pages/Home/Home'));
const NewHome = lazy(() => import('../pages/NewHome/NewHome'));
const Integrations = lazy(() => import('../pages/Integrations/Integrations'));
const Privacy = lazy(() => import('../pages/Privacy/Privacy'));
const Terms = lazy(() => import('../pages/Terms/Terms'));
const Withdraw = lazy(() => import('../pages/Withdraw/Withdraw'));
const Transfer = lazy(() => import('../pages/Transfer/Transfer'));
const UnSupport = lazy(() => import('../pages/UnSupport/UnSupport'));
const Specification = lazy(() => import('../pages/Specification/Specification'));

const routes: RouteObject[] = [
  // {
  //   path: '/:chain/:network',
  //   children: [
  //     // { path: '', element: <Navigate to="home" /> },
  //     { path: 'home', element: <Home /> },
  //     { path: 'withdraw', element: <Withdraw /> }
  //   ]
  // },
  {
    path: '/aG9tZXBhZ2U=123',
    element: <Home />
  },
  {
    path: '/newHome',
    element: <NewHome />
  },
  {
    path: '/3rdparty',
    element: <Integrations />
  },
  {
    path: '/privacy',
    element: <Privacy />
  },
  {
    path: '/terms',
    element: <Terms />
  },
  {
    path: '/withdraw',
    element: <Withdraw />
  },
  {
    path: '/transfer',
    element: <Transfer />
  },
  {
    path: '/transfer/record',
    element: <Record />
  },
  {
    path: '/unsupport',
    element: <UnSupport />
  },
  {
    path: '/home',
    element: <NewHome />
  },
  {
    path: '/faq',
    element: <FAQ />
  },
  {
    path: '/specification',
    element: <Specification />
  },
  {
    path: '/',
    element: <Navigate to="/home" />
  },
  {
    path: '*',
    element: <Navigate to="/unsupport" />
  }
];

const router = createBrowserRouter(routes);

const Routes = () => {
  return <RouterProvider router={router} />;
};

export default Routes;
