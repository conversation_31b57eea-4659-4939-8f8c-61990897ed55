@import url(./fonts.css);
html,
body,
* {
  margin: 0;
  padding: 0;
  font-size: 14px;
  font-family: 'Sora', 'Wix Madefor Display', Inter, sans-serif;
  box-sizing: border-box;
  font-variant-ligatures: no-contextual;
}

p,
h1,
h2,
h3,
h4,
h5,
h6 {
  margin-bottom: 0;
}

body {
  overscroll-behavior: none;
}

ul,
li {
  list-style: none;
}

a {
  text-decoration: none;
}

body {
  background: rgba(0, 0, 0, 0.6);
  min-height: calc(100vh);
}
#root {
  width: 100%;
  height: 100vh;
  overflow-y: auto;
  overflow-x: auto;
}

.main-content {
  width: 100%;
  max-width: 1300px;
  margin: auto;
}

.opacity-0 {
  opacity: 0;
}

.flex {
  display: flex;
}
.flex.column {
  flex-direction: column;
}
.flex.justify-end {
  justify-content: flex-end;
}
.flex.justify-between {
  justify-content: space-between;
}
.flex.justify-center {
  justify-content: center;
}
.flex.items-center {
  align-items: center;
}

.flex.item-end {
  align-items: flex-end;
}

.fadeInUpLite {
  animation-name: fadeInUpLite;
}
@keyframes fadeInUpLite {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0, 20%, 0);
    transform: translate3d(0, 20%, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
  }
}

.spin {
  animation-name: spin;
  animation-duration: 2000ms;
  animation-iteration-count: infinite;
  animation-timing-function: linear;
  animation-direction: reverse;
}
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.ant-select .ant-select-selection-item {
  font-weight: 600;
}
.ant-select-dropdown .ant-select-item-option-content,
.ant-select-selection-search-input {
  font-weight: 600;
}

// cascader active
.ant-select-focused:where(.css-dev-only-do-not-override-ell2og).ant-select-outlined:not(.ant-select-disabled):not(
    .ant-select-customize-input
  ):not(.ant-pagination-size-changer)
  .ant-select-selector {
  border: 1px solid rgba(255, 255, 255, 0.5);
}

// token icon
.token-icon {
  display: block;
  background: none no-repeat center/contain;
  &.USDT {
    background-image: url(../imgs/token_usdt.svg);
  }
  &.JST {
    background-image: url(../imgs/token_jst.svg);
  }
}

// error when network not match
.ant-select.error {
  color: red;
  border: 1px solid #e44848;
  margin-top: 6px;
  overflow: hidden;
  .ant-select-selector {
    background-color: #ff303026;
    border: none;
  }
}
.switch-chain-error-popup {
  background-color: #ff30301a;
  --antd-arrow-background-color: #ff30301a;
  border-radius: 6px;
  .ant-popover-inner {
    background-color: transparent;
    padding: 8px 10px;
  }
}
