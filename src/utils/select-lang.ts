import { find } from 'lodash-es';
import intl from 'react-intl-universal';
import zhCN from '../locales/zh-CN.json';
import zhTC from '../locales/zh-TC.json';
import enUS from '../locales/en-US.json';
export const SUPPOER_LOCALES = [
  {
    label: 'English',
    value: 'en-US'
  },
  {
    label: '简体中文',
    value: 'zh-CN'
  },
  {
    label: '繁体中文',
    value: 'zh-TC'
  }
];
export const LOCALES = {
  'zh-CN': zhCN,
  'zh-TC': zhTC,
  'en-US': enUS
} as const;
export function initIntl() {
  let currentLocale = intl.determineLocale({
    urlLocaleKey: 'lang',
    cookieLocaleKey: 'lang'
  });
  currentLocale = window.localStorage.getItem('lang') || 'en-US';
  if (!find(SUPPOER_LOCALES, { value: currentLocale })) {
    currentLocale = 'en-US';
  }
  window.localStorage.setItem('lang', currentLocale);
  intl.init({
    currentLocale,
    locales: LOCALES
  });
}
initIntl();

export type Lang = keyof typeof LOCALES;
export function getLang(): Lang {
  return (window.localStorage.getItem('lang') as Lang) || 'en-US';
}
export function isInEn() {
  return getLang() === 'en-US';
}

export function setLang(lang: Lang) {
  if (lang === getLang()) {
    return;
  }
  window.localStorage.setItem('lang', lang);
  window.location.reload();
}

export function get(key: string) {
  return intl.get(key);
}

export function getHTML(key: string, variables?: unknown) {
  return intl.getHTML(key, variables);
}
