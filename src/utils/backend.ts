import axios from 'axios';
import { getChainInfo } from './chains';
import Config from '@/config/index';

// 定义通用的API选项类型
interface ApiOptions {
  params?: Record<string, any>;
  headers?: Record<string, string>;
  [key: string]: any;
}

// 使用Web Crypto API实现正确的HMAC-SHA256
const hmacSha256 = async (message: string, secret: string): Promise<string> => {
  const encoder = new TextEncoder();

  // 将密钥转换为CryptoKey
  const key = await crypto.subtle.importKey('raw', encoder.encode(secret), { name: 'HMAC', hash: 'SHA-256' }, false, [
    'sign'
  ]);

  // 对消息进行签名
  const signature = await crypto.subtle.sign('HMAC', key, encoder.encode(message));

  // 将结果转换为base64字符串
  const hashArray = Array.from(new Uint8Array(signature));
  const hashBase64 = btoa(String.fromCharCode(...hashArray));

  return hashBase64;
};

// get Asset Info
export const getAssetInfo = async (options: ApiOptions = {}) => {
  try {
    const { chainId } = getChainInfo(location.search);
    const url = Config.service[chainId].tronlink + '/api/wallet/v2/asset';
    const res = await axios.get(url, options);
    if (res.data.code == 0 && res.data.data != null) {
      return { success: true, data: res.data.data };
    } else {
      return { success: false };
    }
  } catch (error) {
    console.error(`getAssetInfo error: ${error}`);
    return { success: false };
  }
};

export const getEVMAssetInfo = async (options: ApiOptions = {}) => {
  try {
    const { chainId } = getChainInfo(location.search);
    const url = Config.service[chainId].tronlink + '/api/wallet/eth/asset';
    const res = await axios.get(url, options);
    if (res.data.code == 0 && res.data.data != null) {
      return { success: true, data: res.data.data };
    } else {
      return { success: false };
    }
  } catch (error) {
    console.error(`getEvmAssetInfo error: ${error}`);
    return { success: false };
  }
};

//
export const getAssetList = async (options: ApiOptions = {}) => {
  try {
    const { chainId } = getChainInfo(location.search);
    const url = Config.service[chainId].tronlink + '/api/wallet/v2/allAssetList';
    const res = await axios.get(url, options);
    if (res.data.code == 0 && res.data.data != null) {
      return { success: true, data: res.data.data };
    } else {
      return { success: false };
    }
  } catch (error) {
    console.error(`getAssetList error: ${error}`);
    return { success: false };
  }
};

export const getEVMAssetList = async (options: ApiOptions = {}) => {
  try {
    const { chainId } = getChainInfo(location.search);
    const url = Config.service[chainId].tronlink + '/api/wallet/eth/assetList';
    const res = await axios.get(url, options);
    if (res.data.code == 0 && res.data.data != null) {
      return { success: true, data: res.data.data };
    } else {
      return { success: false };
    }
  } catch (error) {
    console.error(`getEVMAssetList error: ${error}`);
    return { success: false };
  }
};

export const getEVMNewAssetList = async (options: ApiOptions = {}) => {
  try {
    const { chainId } = getChainInfo(location.search);
    const url = Config.service[chainId].tronlink + '/api/wallet/eth/newAssetList';
    const res = await axios.get(url, options);
    if (res.data.code == 0 && res.data.data != null) {
      return { success: true, data: res.data.data };
    } else {
      return { success: false };
    }
  } catch (error) {
    console.error(`getEVMNewAssetList error: ${error}`);
    return { success: false };
  }
};

/**
 * 获取统计历史摘要数据
 * 使用HMac-Sha256签名认证
 *
 * 使用示例：
 * ```typescript
 * import { getStatsHistorySummary } from '@/utils/backend';
 *
 * // 调用API获取统计数据
 * const result = await getStatsHistorySummary();
 * if (result.success) {
 *   console.log('统计数据:', result.data);
 * } else {
 *   console.error('获取失败:', result.error);
 * }
 *
 * // 带参数调用
 * const resultWithParams = await getStatsHistorySummary({
 *   params: { startDate: '2024-01-01', endDate: '2024-01-31' }
 * });
 * ```
 */
export const getStatsHistorySummary = async (options: ApiOptions = {}) => {
  try {
    const { statsApi } = Config;

    const endpoint = statsApi.endpoints;
    const method = 'GET';
    const timestamp = Math.floor(Date.now() / 1000); // 秒级时间戳

    // 构建明文：Method + Endpoint + 秒级时间戳
    const plaintext = `${method}${endpoint}${timestamp}`;

    // 使用HMac-Sha256算法生成签名
    const signature = await hmacSha256(plaintext, statsApi.apisecret);

    // 构建完整的URL
    const url = `${statsApi.baseUrl}${endpoint}`;

    // 构建请求头
    const headers = {
      'Content-Type': 'application/json',
      'Timestamp': timestamp.toString(),
      'Authorization': `Apikey ${statsApi.apikey}:${signature}`,
      ...options.headers
    };

    const res = await axios.get(url, {
      ...options,
      headers
    });

    return { success: true, data: res.data };
  } catch (error) {
    console.error(`getStatsHistorySummary error: ${error}`);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return { success: false, error: errorMessage };
  }
};
