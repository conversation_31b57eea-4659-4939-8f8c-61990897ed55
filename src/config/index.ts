const env = import.meta.env.VITE_APP_ENV || '';
import { CHAIN_ID } from './chains';

export const isTest = env === 'test' || env === 'nile';
export const isNile = env === 'nile';
const Config = {
  chain: {
    [CHAIN_ID.TRON_MAINNET]: {
      privateKey: '01',
      fullHost: 'https://api.trongrid.io'
    },
    [CHAIN_ID.TRON_NILE]: {
      privateKey: '01',
      fullHost: 'https://api.nileex.io'
    },
    [CHAIN_ID.TRON_SHASTA]: {
      privateKey: '01',
      fullHost: 'https://api.shasta.trongrid.io/'
    },
    [CHAIN_ID.ETHEREUM_MAINNET]: {
      privateKey: '',
      fullHost: ''
    },
    [CHAIN_ID.ETHEREUM_SEPOLIA]: {
      privateKey: '01',
      fullHost: 'https://eth-sepolia.trongrid.io'
    },
    [CHAIN_ID.BSC_MAINNET]: {
      privateKey: '',
      fullHost: ''
    },
    [CHAIN_ID.BSC_TESTNET]: {
      privateKey: '01',
      fullHost: 'https://bsc-testnet-rpc.publicnode.com'
    },
    [CHAIN_ID.BTTC_MAINNET]: {
      privateKey: '',
      fullHost: ''
    },
    [CHAIN_ID.BTTC_TESTNET]: {
      privateKey: '01',
      fullHost: 'https://pre-rpc.bt.io/'
    }
  },
  SECRET_ID: '8JKSO2PM4M2K45EL',
  SECRET_KEY: 'S8NFNSFJDFJKNFKASNFSJNFKJSN2344SFN2K2',
  statsApi: {
    baseUrl: 'https://open.gasfree.io',
    endpoints: '/tron/api/v1/stats/history/summary',
    apikey: '323a36b3-bb9a-4a5c-92c9-201cda7038bd',
    apisecret: '*******************************************'
  },
  service: {
    [CHAIN_ID.TRON_MAINNET]: {
      tronlink: 'https://list.tronlink.org',
      blockExplorerUrl: 'https://tronscan.org/#/transaction/'
    },
    [CHAIN_ID.TRON_NILE]: {
      tronlink: 'https://niletest.tronlink.org',
      blockExplorerUrl: 'https://nile.tronscan.org/#/transaction/'
    },
    [CHAIN_ID.TRON_SHASTA]: {
      tronlink: 'https://shastalist.tronlink.org',
      blockExplorerUrl: 'https://shasta.tronscan.org/#/transaction/'
    },
    [CHAIN_ID.ETHEREUM_MAINNET]: {
      tronlink: '',
      blockExplorerUrl: ''
    },
    [CHAIN_ID.ETHEREUM_SEPOLIA]: {
      tronlink: 'https://niletest.tronlink.org',
      blockExplorerUrl: 'https://sepolia.etherscan.io/tx/'
    },
    [CHAIN_ID.BSC_MAINNET]: {
      tronlink: '',
      blockExplorerUrl: ''
    },
    [CHAIN_ID.BSC_TESTNET]: {
      tronlink: 'https://niletest.tronlink.org',
      blockExplorerUrl: 'https://testnet.bscscan.com/tx/'
    },
    [CHAIN_ID.BTTC_MAINNET]: {
      tronlink: '',
      blockExplorerUrl: ''
    },
    [CHAIN_ID.BTTC_TESTNET]: {
      tronlink: 'https://niletest.tronlink.org',
      blockExplorerUrl: 'https://testscan.bt.io/#/transaction/'
    }
  },
  gasFreeTransferService: {
    [CHAIN_ID.TRON_MAINNET]: 'https://internel-tron-main-api.gasfree.io',
    [CHAIN_ID.TRON_NILE]: 'http://************:8097/',
    [CHAIN_ID.TRON_SHASTA]: 'http://************:8183/',
    [CHAIN_ID.ETHEREUM_MAINNET]: '',
    [CHAIN_ID.ETHEREUM_SEPOLIA]: 'http://************:8180/',
    [CHAIN_ID.BSC_MAINNET]: '',
    [CHAIN_ID.BSC_TESTNET]: 'http://************:8182/',
    [CHAIN_ID.BTTC_MAINNET]: '',
    [CHAIN_ID.BTTC_TESTNET]: 'http://************:8181/'
  },
  contract: {
    // for test
    [CHAIN_ID.TRON_MAINNET]: {
      factory: 'TFFAMQLZybALaLb4uxHA9RBE7pxhUAjF3U',
      poly: 'TB78aCmseNzAvX7j1oZnoJZutH3AVR8qdH',
      holeAddress: 'T9yD14Nj9j7xAB4dbGeiX9h8unkKHxuWwb',
      unSupportTokens: [
        {
          tokenAddress: 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t',
          symbol: 'USDT'
        }
      ]
    },
    [CHAIN_ID.TRON_NILE]: {
      factory: 'THQGuFzL87ZqhxkgqYEryRAd7gqFqL5rdc',
      poly: 'TTiaCMFbdPNkhC6dbrULZn1vXi22iAvwij',
      holeAddress: 'T9yD14Nj9j7xAB4dbGeiX9h8unkKHxuWwb',
      unSupportTokens: [
        {
          tokenAddress: 'TXYZopYRdj2D9XRtbG411XZZ3kM5VkAeBf',
          symbol: 'USDT'
        }
      ]
    },
    [CHAIN_ID.TRON_SHASTA]: {
      factory: 'TQghdCeVDA6CnuNVTUhfaAyPfTetqZWNpm',
      poly: 'TSTzGyceMQYrjCGPto6qLHUUfmWN4i9kiV',
      holeAddress: 'T9yD14Nj9j7xAB4dbGeiX9h8unkKHxuWwb',
      unSupportTokens: [
        {
          tokenAddress: 'TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs',
          symbol: 'USDT'
        }
      ]
    },
    [CHAIN_ID.ETHEREUM_MAINNET]: {
      factory: '',
      poly: '',
      holeAddress: '******************************************',
      unSupportTokens: []
    },
    [CHAIN_ID.ETHEREUM_SEPOLIA]: {
      factory: '******************************************',
      poly: '******************************************',
      holeAddress: '******************************************',
      unSupportTokens: []
    },
    [CHAIN_ID.BSC_MAINNET]: {
      factory: '',
      poly: '',
      holeAddress: '******************************************',
      unSupportTokens: []
    },
    [CHAIN_ID.BSC_TESTNET]: {
      factory: '******************************************',
      poly: '******************************************',
      holeAddress: '******************************************',
      unSupportTokens: [
        {
          tokenAddress: '******************************************',
          symbol: 'USDT'
        }
      ]
    },
    [CHAIN_ID.BTTC_MAINNET]: {
      factory: '',
      poly: '',
      holeAddress: '******************************************',
      unSupportTokens: []
    },
    [CHAIN_ID.BTTC_TESTNET]: {
      factory: '0xefD0315CbdCEFa96189F224c4B35C0fb8A1d24E0',
      poly: '0x1c44798721463FfB9B0651F520163247cf0aac64',
      holeAddress: '******************************************',
      unSupportTokens: [
        {
          tokenAddress: '******************************************',
          symbol: '"USDT"'
        }
      ]
    }
  },
  feeLimit: 200000000,
  defaultPrecision: 1e6,
  trxPrecision: 1e6,
  holeAddress: 'T9yD14Nj9j7xAB4dbGeiX9h8unkKHxuWwb',
  tronscanUrl: 'https://tronscan.org/#',
  support_chain: [CHAIN_ID.TRON_MAINNET],
  providers: {
    [CHAIN_ID.TRON_MAINNET]: '',
    [CHAIN_ID.ETHEREUM_MAINNET]: '',
    [CHAIN_ID.BSC_MAINNET]: '',
    [CHAIN_ID.BTTC_MAINNET]: ''
  },
  transferDefaultDeadlines: {
    [CHAIN_ID.TRON_MAINNET]: 180,
    [CHAIN_ID.ETHEREUM_MAINNET]: 180,
    [CHAIN_ID.BSC_MAINNET]: 180,
    [CHAIN_ID.BTTC_MAINNET]: 180
  }
};

let devConfig = {};
if (env === 'test' || env === 'nile') {
  devConfig = {
    service: {
      [CHAIN_ID.TRON_MAINNET]: {
        tronlink: 'https://list.tronlink.org',
        blockExplorerUrl: 'https://tronscan.org/#/transaction/'
      },
      [CHAIN_ID.TRON_NILE]: {
        tronlink: 'https://niletest.tronlink.org',
        blockExplorerUrl: 'https://nile.tronscan.org/#/transaction/'
      },
      [CHAIN_ID.TRON_SHASTA]: {
        tronlink: 'https://shastalist.tronlink.org',
        blockExplorerUrl: 'https://shasta.tronscan.org/#/transaction/'
      },
      [CHAIN_ID.ETHEREUM_MAINNET]: {
        tronlink: '',
        blockExplorerUrl: ''
      },
      [CHAIN_ID.ETHEREUM_SEPOLIA]: {
        tronlink: 'https://niletest.tronlink.org',
        blockExplorerUrl: 'https://sepolia.etherscan.io/tx/'
      },
      [CHAIN_ID.BSC_MAINNET]: {
        tronlink: '',
        blockExplorerUrl: ''
      },
      [CHAIN_ID.BSC_TESTNET]: {
        tronlink: 'https://niletest.tronlink.org',
        blockExplorerUrl: 'https://testnet.bscscan.com/tx/'
      },
      [CHAIN_ID.BTTC_MAINNET]: {
        tronlink: '',
        blockExplorerUrl: ''
      },
      [CHAIN_ID.BTTC_TESTNET]: {
        tronlink: 'https://niletest.tronlink.org',
        blockExplorerUrl: 'https://testscan.bt.io/#/transaction/'
      }
    },
    gasFreeTransferService: {
      [CHAIN_ID.TRON_MAINNET]: 'https://internel-tron-main-api.gasfree.io',
      [CHAIN_ID.TRON_NILE]: 'http://************:8097/',
      [CHAIN_ID.TRON_SHASTA]: 'http://************:8183/',
      [CHAIN_ID.ETHEREUM_MAINNET]: '',
      [CHAIN_ID.ETHEREUM_SEPOLIA]: 'http://************:8180/',
      [CHAIN_ID.BSC_MAINNET]: '',
      [CHAIN_ID.BSC_TESTNET]: 'http://************:8182/',
      [CHAIN_ID.BTTC_MAINNET]: '',
      [CHAIN_ID.BTTC_TESTNET]: 'http://************:8181/'
    },
    contract: {
      // for test
      [CHAIN_ID.TRON_MAINNET]: {
        factory: 'TFFAMQLZybALaLb4uxHA9RBE7pxhUAjF3U',
        poly: 'TB78aCmseNzAvX7j1oZnoJZutH3AVR8qdH',
        holeAddress: 'T9yD14Nj9j7xAB4dbGeiX9h8unkKHxuWwb',
        unSupportTokens: [
          {
            tokenAddress: 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t',
            symbol: 'USDT'
          }
        ]
      },
      [CHAIN_ID.TRON_NILE]: {
        factory: 'THQGuFzL87ZqhxkgqYEryRAd7gqFqL5rdc',
        poly: 'TTiaCMFbdPNkhC6dbrULZn1vXi22iAvwij',
        holeAddress: 'T9yD14Nj9j7xAB4dbGeiX9h8unkKHxuWwb',
        unSupportTokens: [
          {
            tokenAddress: 'TXYZopYRdj2D9XRtbG411XZZ3kM5VkAeBf',
            symbol: 'USDT'
          }
        ]
      },
      [CHAIN_ID.TRON_SHASTA]: {
        factory: 'TQghdCeVDA6CnuNVTUhfaAyPfTetqZWNpm',
        poly: 'TSTzGyceMQYrjCGPto6qLHUUfmWN4i9kiV',
        holeAddress: 'T9yD14Nj9j7xAB4dbGeiX9h8unkKHxuWwb',
        unSupportTokens: [
          {
            tokenAddress: 'TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs',
            symbol: 'USDT'
          }
        ]
      },
      [CHAIN_ID.ETHEREUM_MAINNET]: {
        factory: '',
        poly: '',
        holeAddress: '******************************************',
        unSupportTokens: []
      },
      [CHAIN_ID.ETHEREUM_SEPOLIA]: {
        factory: '******************************************',
        poly: '******************************************',
        holeAddress: '******************************************',
        unSupportTokens: []
      },
      [CHAIN_ID.BSC_MAINNET]: {
        factory: '',
        poly: '',
        holeAddress: '******************************************',
        unSupportTokens: []
      },
      [CHAIN_ID.BSC_TESTNET]: {
        factory: '******************************************',
        poly: '******************************************',
        holeAddress: '******************************************',
        unSupportTokens: [
          {
            tokenAddress: '******************************************',
            symbol: 'USDT'
          }
        ]
      },
      [CHAIN_ID.BTTC_MAINNET]: {
        factory: '',
        poly: '',
        holeAddress: '******************************************',
        unSupportTokens: []
      },
      [CHAIN_ID.BTTC_TESTNET]: {
        factory: '0xefD0315CbdCEFa96189F224c4B35C0fb8A1d24E0',
        poly: '0x1c44798721463FfB9B0651F520163247cf0aac64',
        holeAddress: '******************************************',
        unSupportTokens: [
          {
            tokenAddress: '******************************************',
            symbol: '"USDT"'
          }
        ]
      }
    },
    chain: {
      [CHAIN_ID.TRON_MAINNET]: {
        privateKey: '01',
        fullHost: 'https://api.trongrid.io'
      },
      [CHAIN_ID.TRON_NILE]: {
        privateKey: '01',
        fullHost: 'https://api.nileex.io'
      },
      [CHAIN_ID.TRON_SHASTA]: {
        privateKey: '01',
        fullHost: 'https://api.shasta.trongrid.io/'
      },
      [CHAIN_ID.ETHEREUM_MAINNET]: {
        privateKey: '',
        fullHost: ''
      },
      [CHAIN_ID.ETHEREUM_SEPOLIA]: {
        privateKey: '01',
        fullHost: 'https://eth-sepolia.trongrid.io'
      },
      [CHAIN_ID.BSC_MAINNET]: {
        privateKey: '',
        fullHost: ''
      },
      [CHAIN_ID.BSC_TESTNET]: {
        privateKey: '01',
        fullHost: 'https://bsc-testnet-rpc.publicnode.com'
      },
      [CHAIN_ID.BTTC_MAINNET]: {
        privateKey: '',
        fullHost: ''
      },
      [CHAIN_ID.BTTC_TESTNET]: {
        privateKey: '01',
        fullHost: 'https://pre-rpc.bt.io/'
      }
    },
    SECRET_ID: '8JKSO2PM4M2K45EL',
    SECRET_KEY: 'S8NFNSFJDFJKNFKASNFSJNFKJSN2344SFN2K2',
    // Stats API configuration
    statsApi: {
      baseUrl: import.meta.env.DEV ? '/stats-api' : 'https://open-test.gasfree.io',
      endpoints: '/nile/api/v1/stats/history/summary',
      apikey: 'd98a31ea-1953-4803-8d74-0638751208ff',
      apisecret: 'dqL9cQjkAORCZFXT4OKl5T-by2sxABQM2aweOOfP7gg'
    },
    support_chain: [
      CHAIN_ID.TRON_NILE, // 开发环境优先使用Nile测试网
      CHAIN_ID.TRON_MAINNET,
      CHAIN_ID.TRON_SHASTA,
      CHAIN_ID.ETHEREUM_SEPOLIA,
      CHAIN_ID.BSC_TESTNET,
      CHAIN_ID.BTTC_TESTNET
    ],
    providers: {
      [CHAIN_ID.TRON_NILE]: 'TCETRh3aED4kdkaYQY7CcxeTJtrQvwBpNT',
      [CHAIN_ID.TRON_SHASTA]: 'TCETRh3aED4kdkaYQY7CcxeTJtrQvwBpNT',
      [CHAIN_ID.ETHEREUM_SEPOLIA]: '******************************************',
      [CHAIN_ID.BSC_TESTNET]: '******************************************',
      [CHAIN_ID.BTTC_TESTNET]: '******************************************'
    },
    transferDefaultDeadlines: {
      [CHAIN_ID.TRON_NILE]: 180,
      [CHAIN_ID.TRON_SHASTA]: 180,
      [CHAIN_ID.ETHEREUM_SEPOLIA]: 180,
      [CHAIN_ID.BSC_TESTNET]: 180,
      [CHAIN_ID.BTTC_TESTNET]: 180
    }
  };
}
export default Object.assign(Config, devConfig);
