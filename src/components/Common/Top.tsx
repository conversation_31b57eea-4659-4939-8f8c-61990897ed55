import { useCallback, useEffect, useMemo, useState } from 'react';
import { observer } from 'mobx-react-lite';
import intl from 'react-intl-universal';
import styles from './common.module.scss';
import { ConnectButton } from '../ConnectButton/ConnectButton.tsx';
import { Drawer, Popover } from 'antd';
import { useLocation, useNavigate } from 'react-router-dom';
import { useIsMobile } from '../../hooks/useIsMobile';
import { CloseButton } from '../CloseButton.tsx';
import { setLang } from '../../utils/select-lang.ts';
import { useLang } from '../../hooks/useLang.ts';
import { NetworkErrorPopover, SwitchChain } from '../SwitchChain/SwitchChain.tsx';
import classnames from 'classnames';
import { useWallet } from '../WalletProvider/useWallet.ts';
import { isInEn } from '../../utils/select-lang';
import { LangSelect } from '../Common/LangSelect';

const Top = observer(() => {
  const isMobile = useIsMobile();
  const { pathname, search } = useLocation();
  const currentRoute = useMemo(() => pathname.slice(1) || 'home', [pathname]);
  const [open, setOpen] = useState(false);

  const env = import.meta.env.VITE_APP_ENV || '';

  const shouldShowConnectButton = useMemo(() => {
    if (env !== 'nile') return true;

    const searchParams = new URLSearchParams(search);
    return searchParams.has('chain');
  }, [env, search]);

  const Nav = useMemo(
    () => (
      <nav className={styles.nav}>
        {/* <a href="home" className={currentRoute.endsWith('home') ? styles.current : ''}>
          {intl.get('menu_home')}
        </a> */}
        <a
          href="withdraw"
          className={currentRoute.endsWith('withdraw') ? styles.current : ''}
          onClick={() =>
            window.gtag &&
            window.gtag('event', pathname.startsWith('/withdraw') ? 'withdraw_header_withdraw' : undefined)
          }
        >
          {intl.get('menu_withdraw')}
        </a>
        <a
          href={isInEn() ? '/docs/GasFree_specification.html' : '/docs/GasFree_specification_cn.html'}
          target="docs"
          onClick={() =>
            window.gtag && window.gtag('event', pathname.startsWith('/withdraw') ? 'withdraw_header_doc' : undefined)
          }
        >
          {intl.get('footer_docs')}
        </a>
        {env === 'nile' && (
          <a
            href="https://nileex.io/join/getJoinPage"
            target="faucet"
            onClick={() =>
              window.gtag &&
              window.gtag('event', pathname.startsWith('/withdraw') ? 'withdraw_header_faucets' : undefined)
            }
          >
            {intl.get('nile_faucet')}
          </a>
        )}
        {/* <a href="transfer" className={currentRoute.startsWith('transfer') ? styles.current : ''}>
          {intl.get('menu_transfer')}
        </a> */}
        {currentRoute === 'transfer' && (
          <a
            href={currentRoute === 'transfer' ? 'transfer/record' : ''}
            className={currentRoute.includes('record') ? styles.current : ''}
          >
            {intl.get('menu_record')}
          </a>
        )}
      </nav>
    ),
    [currentRoute]
  );
  const { lang } = useLang();

  const navigate = useNavigate();
  function onGoRecord() {
    const target = '/transfer/record';
    if (currentRoute !== target) {
      navigate(target);
    }
  }
  const { isNetworkMatch } = useWallet();
  return (
    <>
      {env === 'nile' && (
        <div className={styles.topAnnouncement}>
          <p>{intl.get('top_nile_reminder')}</p>
        </div>
      )}
      <div className={styles.top}>
        {!isMobile ? (
          <>
            <h1
              onClick={() =>
                window.gtag &&
                window.gtag('event', pathname.startsWith('/withdraw') ? 'withdraw_top_left_icon' : undefined)
              }
            ></h1>
            {Nav}
          </>
        ) : null}

        <div className={styles.buttonWrap}>
          {env === 'test' && !isMobile ? (
            <NetworkErrorPopover isError={!isNetworkMatch}>
              <SwitchChain isError={!isNetworkMatch} style={{ marginRight: '10px' }} />{' '}
            </NetworkErrorPopover>
          ) : null}
          {!isMobile && currentRoute.includes('transfer') ? (
            <span onClick={onGoRecord} className={styles.recordIcon}></span>
          ) : null}
          {shouldShowConnectButton && (
            <ConnectButton
              classNames={styles.connectButton}
              onClick={() =>
                window.gtag &&
                window.gtag('event', pathname.startsWith('/withdraw') ? 'withdraw_header_connect' : undefined)
              }
            />
          )}
          {!isMobile && <LangSelect></LangSelect>}
        </div>

        {isMobile ? (
          <>
            {env === 'test' ? (
              <NetworkErrorPopover isError={!open && !isNetworkMatch}>
                <span onClick={() => setOpen(true)} className={styles.menubar}></span>
              </NetworkErrorPopover>
            ) : (
              <span onClick={() => setOpen(true)} className={styles.menubar}></span>
            )}
            <Drawer
              maskClosable
              open={open}
              placement="top"
              height="237px"
              onClose={() => {
                setOpen(false);
              }}
              styles={{
                header: { display: 'none' },
                body: {
                  backgroundColor: '#131121',
                  padding: '42px 15px 0px'
                }
              }}
            >
              <div className={styles.menuWrap}>
                <CloseButton
                  onClick={() => setOpen(false)}
                  style={{ position: 'absolute', top: '5px', right: '0px' }}
                />
                {Nav}
                <div className={styles.divider}></div>
                {env === 'test' && (
                  <div className={styles.footMenuItem}>
                    <span className="label">{intl.get('switch_chain')}</span>
                    <SwitchChain />
                  </div>
                )}
                <div className={classnames(styles.langSelect, styles.footMenuItem)}>
                  <span className="label">{intl.get('switch_lang')}</span>
                  <span className="desc">
                    <span onClick={() => setLang('en-US')} className={`${lang === 'en-US' ? 'current' : ''}`}>
                      English
                    </span>
                    <i>/</i>
                    <span onClick={() => setLang('zh-TC')} className={`${lang === 'zh-TC' ? 'current' : ''}`}>
                      繁体中文
                    </span>
                    <i>/</i>
                    <span onClick={() => setLang('zh-CN')} className={`${lang === 'zh-CN' ? 'current' : ''}`}>
                      简体中文
                    </span>
                  </span>
                </div>
              </div>
            </Drawer>
          </>
        ) : null}
      </div>
    </>
  );
});

export default Top;
