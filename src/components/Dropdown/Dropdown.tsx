import React, { useState, useRef, PropsWithChildren, CSSProperties } from 'react';
import styles from './Dropdown.module.scss';

export interface DropdownItem {
  href: string;
  content: React.ReactNode;
}

export interface DropdownProps {
  items: React.ReactNode[];
  style?: CSSProperties;
}

const Dropdown: React.FC<PropsWithChildren<DropdownProps>> = ({ children, items, style }) => {
  const [open, setOpen] = useState(false);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  const handleMouseEnter = () => {
    if (timerRef.current) clearTimeout(timerRef.current);
    setOpen(true);
  };

  const handleMouseLeave = () => {
    timerRef.current = setTimeout(() => setOpen(false), 150);
  };

  return (
    <div
      className={`${styles.dropdown} ${open ? 'active' : ''}`}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div className={styles.trigger}>{children}</div>
      <div className={`${styles.menu} ${open ? styles.open : ''}`} style={style}>
        {items}
      </div>
    </div>
  );
};

export default Dropdown;
