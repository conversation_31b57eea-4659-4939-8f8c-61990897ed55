.dropdown {
  position: relative;
  display: inline-block;
}

.trigger {
  cursor: pointer;
}

.trigger:hover,
.active {
  color: #33ffa9;
}

.menu {
  position: absolute;
  left: -20px;
  transform: translateX(0%) translateY(-10px);
  margin-top: 8px;
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.15);
  background: #212121;
  opacity: 0;
  pointer-events: none;
  transition:
    opacity 0.3s,
    transform 0.3s;
  z-index: 1000;
  padding: 10px;
  transform-origin: top center;
}

.open {
  opacity: 1;
  pointer-events: auto;
  transform: translateX(0%) translateY(0);
}
