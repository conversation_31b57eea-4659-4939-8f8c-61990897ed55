import styles from './Footer.module.scss';
import { LangSelect } from '../Common/LangSelect';
import { useIsMobile } from '../../hooks/useIsMobile';
import intl from 'react-intl-universal';
import { isInEn } from '../../utils/select-lang';

export function Footer() {
  const isMobile = useIsMobile();
  return (
    <div className={styles.footer}>
      <div className="inner">
        <div className="link-wrap">
          <a href="/3rdparty" onClick={() => window.gtag && window.gtag('event', 'withdraw_footer_3rdparty')}>
            {intl.get('footer_3rdparty')}
          </a>
          <a href="/join" onClick={() => window.gtag && window.gtag('event', 'withdraw_footer_join')}>
            {intl.get('footer_join')}
          </a>
          <a href="/withdraw" onClick={() => window.gtag && window.gtag('event', 'withdraw_footer_withdraw')}>
            {intl.get('footer_withdraw')}
          </a>
          <a
            href={isInEn() ? '/docs/GasFree_specification.html' : '/docs/GasFree_specification_cn.html'}
            target="_blank"
            rel="noopener noreferrer"
            onClick={() => window.gtag && window.gtag('event', 'withdraw_footer_doc')}
          >
            {intl.get('footer_docs')}
          </a>
          <a
            href="https://developer.gasfree.io/"
            target="_blank"
            rel="noopener noreferrer"
            onClick={() => window.gtag && window.gtag('event', 'withdraw_footer_devcenter')}
          >
            {intl.get('footer_devcenter')}
          </a>
          <a
            href="https://github.com/DeFi-JUST/GasFree"
            target="_blank"
            rel="noopener noreferrer"
            onClick={() => window.gtag && window.gtag('event', 'withdraw_footer_github')}
          >
            GitHub
          </a>
          <a
            href="https://nileex.io/join"
            target="_blank"
            rel="noopener noreferrer"
            onClick={() => window.gtag && window.gtag('event', 'withdraw_footer_faucets')}
          >
            {intl.get('footer_faucets')}
          </a>
          <a href="/whitepaper" onClick={() => window.gtag && window.gtag('event', 'withdraw_footer_whitepaper')}>
            {intl.get('footer_whitepaper')}
          </a>
          <a href="/help" onClick={() => window.gtag && window.gtag('event', 'withdraw_footer_help_en')}>
            {intl.get('footer_help_en')}
          </a>
          <a href="/help/cn" onClick={() => window.gtag && window.gtag('event', 'withdraw_footer_help_cn')}>
            {intl.get('footer_help_cn')}
          </a>
          <a href="/faq" onClick={() => window.gtag && window.gtag('event', 'withdraw_footer_faq')}>
            {intl.get('footer_faq')}
          </a>
          <a href="/tech-form" onClick={() => window.gtag && window.gtag('event', 'withdraw_footer_tech_form')}>
            {intl.get('footer_tech_form')}
          </a>
          <a href="/feedback" onClick={() => window.gtag && window.gtag('event', 'withdraw_footer_feedback')}>
            {intl.get('footer_feedback')}
          </a>
          <a href="/privacy" onClick={() => window.gtag && window.gtag('event', 'withdraw_footer_privacy')}>
            {intl.get('footer_privacy')}
          </a>
          <a href="/terms" onClick={() => window.gtag && window.gtag('event', 'withdraw_footer_terms')}>
            {intl.get('footer_terms')}
          </a>
        </div>
        <div className="icon-wrap flex">
          <span
            className="icon twitter"
            onClick={() => window.gtag && window.gtag('event', 'withdraw_footer_twitter')}
          ></span>
          <span
            className="icon telegram"
            onClick={() => window.gtag && window.gtag('event', 'withdraw_footer_telegram')}
          ></span>
        </div>
      </div>
    </div>
  );
}
