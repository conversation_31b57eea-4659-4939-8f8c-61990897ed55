.walletButtonWrap {
  position: relative;
}
.connectButton {
  font-size: 14px;
  font-weight: 600;
  line-height: 36px;
  text-align: left;
  color: #33ffa9;
  border-radius: 6px;
  background-color: rgba(255, 255, 255, 0.1);
  padding: 8px 15px;
  i,
  img {
    width: 16px;
    height: 16px;
  }
}
.connectedButton {
  font-size: 14px;
  font-weight: 600;
  line-height: 36px;
  text-align: left;
  padding: 10px;
  display: flex;
  align-items: center;
  padding: 0 10px;
  background-color: rgba(255, 255, 255, 0.15);
  i {
    margin-right: 5px;
    margin-bottom: 2px;
  }
  i,
  img {
    width: 16px;
    height: 16px;
  }
}
.gasFree {
  margin-left: 10px;
  border-radius: 4px;
  background-color: #33ffa9;
  line-height: 20px;
  font-family: Inter;
  font-size: 12px;
  font-weight: 700;
  text-align: center;
  color: #242237;
  padding: 0px 4px;
}
.normal {
  margin-left: 4px;
  font-size: 14px;
  font-weight: 600;
}
.connectDropdown {
  position: absolute;
  margin: auto;
  top: 100%;
  right: 0;
  left: 0;
  display: inline-flex;
  z-index: 10;
  background-color: #1c1a30;
}

.connectDropdownList {
  margin: 0 auto;
  width: 100%;
  list-style-type: none;
  border-radius: 6px;
  flex-direction: column;
  position: relative;
  top: 5px;
  padding: 5px 0;
  overflow: hidden;
  height: min-content;
  // background: #2c2d30;
}

.menuitem {
  display: block;
  margin: auto;
  color: white;
  cursor: pointer;
  white-space: nowrap;
  box-sizing: border-box;
  width: 100%;
  height: 37px;
  border: none;
  outline: none;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 0 20px;
  font-size: 14px;
  font-weight: 600;
  display: flex;

  &:not([disabled]):hover {
    background-color: #373c47;
  }
}
.changeAddress {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 15px 0;
}

@media screen and (max-width: 750px), screen and (orientation: landscape) and (max-width: 1024px) {
  .walletButtonWrap {
    position: relative;
    width: calc(100vw - 30px);
    justify-content: center;
    display: flex;
    margin: 0 auto;
  }
  .mobileLogo {
    width: 24px;
    height: 24px;
    position: absolute;
    left: 10px;
    top: 3px;
    background: url(../../assets/imgs/m/logo.svg) no-repeat center/contain;
  }
  .connectButton {
    line-height: 30px;
    height: 30px;
    margin-top: 0;
  }
  .connectedButton {
    position: relative;
    z-index: 10;
    line-height: 30px;
    height: 30px;
  }
  .connectDropdown {
    width: 100%;
    z-index: 9;
    top: 0;
    position: fixed;
    background-color: #131121;
  }
  .connectDropdownList {
    padding-top: 45px;
    top: 0;
    width: 100%;
    .changeAddress {
      height: unset;
      padding: 20px 0;
    }
  }
}
