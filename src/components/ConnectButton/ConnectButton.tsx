import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Button } from '../ConnectModal/Button.tsx';
import { useWallet } from '../WalletProvider/useWallet.ts';
import styles from './ConnectButton.module.scss';
import { Collapse } from '@tronweb3/tronwallet-adapter-react-ui';
import intl from 'react-intl-universal';
import { CloseButton } from '../CloseButton.tsx/index.tsx';
import { useIsMobile } from '../../hooks/useIsMobile.ts';
import { useNavigate } from 'react-router-dom';

interface Props {
  classNames?: string;
}
export function ConnectButton({ classNames }: Props) {
  const isMobile = useIsMobile();
  const { address, gasFreeAddress, adapterName, wallets, disconnect, showConnectModal } = useWallet();
  const [showGasFreeAddress, setShowGasFreeAddress] = useState(false);
  const wallet = useMemo(() => wallets.find(wallet => wallet.adapter.name === adapterName), [wallets, adapterName]);
  function handleConnect() {
    window.gtag &&
      window.gtag('event', window.location.pathname.startsWith('/withdraw') ? 'withdraw_header_connect' : undefined);
    showConnectModal();
  }
  const displayedAddr = useMemo(() => {
    const addr = showGasFreeAddress ? gasFreeAddress : address;
    // if (Object.prototype.toString.call(addr) && addr.chainId) {
    //   return;
    // }
    return addr ? `${addr.slice(0, 6)}...${addr.slice(-6)}` : '';
  }, [address, gasFreeAddress, showGasFreeAddress]);

  const [dropdownVisible, setDropdownVisible] = useState(false);
  const [copied, setCopied] = useState(false);
  const ref = useRef<HTMLUListElement>(null);
  const changeAddressType = () => {
    window.gtag &&
      window.gtag(
        'event',
        window.location.pathname.startsWith('/withdraw') ? 'withdraw_header_connected_togasfree' : undefined
      );
    setShowGasFreeAddress(!showGasFreeAddress);
    setTimeout(() => {
      hideDropdown();
    }, 100);
  };
  const copyAddress = () => {
    let addr = address;
    if (showGasFreeAddress) {
      addr = gasFreeAddress;
    }
    if (addr) {
      window.gtag &&
        window.gtag(
          'event',
          window.location.pathname.startsWith('/withdraw') ? 'withdraw_header_connected_copy' : undefined
        );
      copyData(addr);
      setCopied(true);
      setTimeout(() => {
        setCopied(false);
        hideDropdown();
      }, 300);
    }
  };
  function changeWallet() {
    window.gtag &&
      window.gtag(
        'event',
        window.location.pathname.startsWith('/withdraw') ? 'withdraw_header_connected_disconnect' : undefined
      );
    showConnectModal();
    hideDropdown();
  }

  const openDropdown = useCallback(function () {
    setDropdownVisible(true);
  }, []);

  const hideDropdown = useCallback(function () {
    setDropdownVisible(false);
  }, []);
  const handleDisconnect = useCallback(
    async function () {
      window.gtag &&
        window.gtag(
          'event',
          window.location.pathname.startsWith('/withdraw') ? 'withdraw_header_connected_disconnect' : undefined
        );
      disconnect();
      hideDropdown();
    },
    [hideDropdown, disconnect]
  );

  useEffect(() => {
    const listener = (event: Event) => {
      const node = ref.current;
      if (!node || node.contains(event.target as Node)) return;
      hideDropdown();
    };

    document.addEventListener('mousedown', listener);
    document.addEventListener('touchstart', listener);

    return () => {
      document.removeEventListener('mousedown', listener);
      document.removeEventListener('touchstart', listener);
    };
  }, [ref, hideDropdown]);

  const nav = useNavigate();
  function goHomePage() {
    nav('/');
  }
  return (
    <div className={`${styles.walletButtonWrap}${classNames ? ' ' + classNames : ''}`}>
      {isMobile && <div className={styles.mobileLogo} onClick={goHomePage}></div>}
      {!address ? (
        <Button className={styles.connectButton} onClick={handleConnect}>
          {intl.get('connect_wallet')}
        </Button>
      ) : (
        <>
          <Button onClick={openDropdown} className={styles.connectedButton} icon={wallet?.adapter.icon}>
            {displayedAddr} {showGasFreeAddress ? <span className={styles.gasFree}>GasFree</span> : null}
          </Button>
          <Collapse className={styles.connectDropdown} isOpen={dropdownVisible}>
            {isMobile && (
              <CloseButton
                onClick={hideDropdown}
                style={{ position: 'absolute', top: '18px', right: '15px', zIndex: 12 }}
              ></CloseButton>
            )}

            <ul ref={ref} className={styles.connectDropdownList} role="menu">
              <li role="menuitem" onClick={changeAddressType} className={`${styles.changeAddress} ${styles.menuitem}`}>
                {intl.get('change_to')}
                {showGasFreeAddress ? (
                  <span className={styles.normal}>{intl.get('normal_addr')}</span>
                ) : (
                  <span className={styles.gasFree}>GasFree</span>
                )}
              </li>
              {address && (
                <li onClick={copyAddress} role="menuitem" className={styles.menuitem}>
                  {copied ? intl.get('copied') : intl.get('copy_addr')}
                </li>
              )}
              {wallets.length > 1 && (
                <li onClick={changeWallet} role="menuitem" className={styles.menuitem}>
                  {intl.get('change_wallet')}
                </li>
              )}
              {address && (
                <li onClick={handleDisconnect} role="menuitem" className={styles.menuitem}>
                  {intl.get('disconnect_wallet')}
                </li>
              )}
            </ul>
          </Collapse>
        </>
      )}
    </div>
  );
}

function copyData(copyText: string) {
  const textArea = document.createElement('textarea');
  textArea.value = copyText;
  document.body.appendChild(textArea);
  textArea.select();
  textArea.style.position = 'fixed';
  textArea.style.top = '-9999px';
  textArea.style.left = '-9999px';
  document.execCommand('copy');
  textArea.blur();
}
