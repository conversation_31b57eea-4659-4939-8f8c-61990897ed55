import { PropsWithChildren } from 'react';
// import Top from '../Common/Top';
// import { Footer } from '../Footer/Footer';
import styles from './PageContainer.module.scss';
import classNames from 'classnames';
import { Header } from '@/pages/NewHome/Header/Header';
import Footer from '@/pages/NewHome/Footer/Footer';

export function PageContainer(props: PropsWithChildren<{ className?: string }>) {
  return (
    <div className={classNames(styles.pageContainer, props.className)}>
      <Header showConnectButton />
      {props.children}
      <Footer />
    </div>
  );
}
