import { <PERSON>r, Popover } from 'antd';
import styles from './SwitchChain.module.scss';
import { CSSProperties, PropsWithChildren, useCallback } from 'react';
import { CHAIN_OPTIONS, SUPPORTED_CHAINS } from '../../config/chains';
import { useChainInfo } from '../../hooks/useChainInfo';
import { useSearchParams } from 'react-router-dom';
import { SelectArrowDown } from '../icons/SelectArrowDown';
import { SelectArrowRight } from '../icons/SelectArrowRight';
import classNames from 'classnames';
import intl from 'react-intl-universal';
import { useWallet } from '../WalletProvider/useWallet';

export function SwitchChain({ style, isError }: { style?: CSSProperties; isError?: boolean }) {
  const { chain, chainId } = useChainInfo();
  const { address } = useWallet();
  const [searchParams, setSearchParams] = useSearchParams();
  const onChange = useCallback(
    function (v: string[]) {
      setSearchParams({
        ...searchParams,
        chain: v[1]
      });
      location.reload();
    },
    [searchParams, setSearchParams]
  );

  function optionRender(option: { value: string; label: string }) {
    if (SUPPORTED_CHAINS.includes(option.value)) {
      return (
        <div>
          <span className={`icon ${option.value}`}></span>
          <span>{option.label}</span>
        </div>
      );
    }
    return <div>{option.label}</div>;
  }

  // const content = useMemo(
  //   () => (
  //     <div className="alert">
  //       <span className="icon"></span>
  //       <span>{intl.get('network_not_match')}</span>
  //     </div>
  //   ),
  //   []
  // );

  return (
    <Cascader
      defaultValue={[chain, chainId]}
      className={classNames(styles.cascader, isError && !!address ? 'error' : '')}
      allowClear={false}
      displayRender={displayRender}
      options={CHAIN_OPTIONS}
      onChange={onChange}
      popupClassName="switch-chain-popup"
      optionRender={optionRender}
      expandTrigger="hover"
      expandIcon={
        <div style={{ height: '100%', display: 'flex', alignItems: 'center', marginLeft: 20 }}>
          <SelectArrowRight />
        </div>
      }
      suffixIcon={<SelectArrowDown />}
      style={{ height: '36px', marginTop: 0, ...style }}
    />
  );
}

export function NetworkErrorPopover({ isError, children }: PropsWithChildren<{ isError: boolean }>) {
  const { address, switchChainIfNeed } = useWallet();
  return isError && !!address ? (
    <Popover
      open
      content={
        <div className={styles.errorContent}>
          <span className={styles.icon}></span>
          <span className={styles.text}>{intl.get('network_not_match')}</span>
          <span className={styles.switch} onClick={switchChainIfNeed}>
            {intl.get('switch_network')}
          </span>
        </div>
      }
      overlayClassName="switch-chain-error-popup"
    >
      {children}
    </Popover>
  ) : (
    children
  );
}

const displayRender = (label: string[]) => {
  return (
    <div className="display-label">
      <span className={`icon ${label[0].toLowerCase()}`}></span>
      <span>{label.join(' ')}</span>
    </div>
  );
};
