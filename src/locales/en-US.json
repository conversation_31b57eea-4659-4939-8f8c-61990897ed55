{"menu_home": "Home", "menu_withdraw": "Withdraw <PERSON>ken", "menu_transfer": "Transfer", "menu_record": "Record", "switch_lang": "Switch Language", "switch_chain": "Switch Network", "disconnect_wallet": "Disconnect", "connect_wallet": "Connect Wallet", "change_wallet": "Change wallet", "wallet_detected": "Detected", "wallet_not_found": "NotFound", "connect_a_wallet": "Connect a wallet<br />to continue", "copy_addr": "Copy address", "change_to": "Change to", "normal_addr": "Normal", "copied": "<PERSON>pied", "switch_network": "Switch network", "connect_wallet_continue": "Connect a wallet<br />to continue", "network_not_match": "Network mismatch with wallet", "home_top_desc": "The GasFree model provides a seamless transfer experience, eliminating the need for native tokens (e.g., TRX) to pay gas for transaction. By simplifying on-chain operations, it allows users to focus on asset management and transactions, improving the efficiency of digital asset use.", "home_start": "Get Started", "home_tx_count": "Transaction Volume", "home_user_count": "User Count", "home_why_gasfree": "Why GasFree", "home_feature_title_1": "<span class=\"em\">High Performance</span>\nand Scalability", "home_feature_desc_1": "Using off-chain signing, batch processing, and GasFreeFactory optimizations, GasFree reduces on-chain interactions, minimizing congestion and delays for fast response and high throughput. With Nonce and Create2, it ensures high performance and scalability.", "home_feature_title_2": "<span class=\"em\">Gas-Free</span>\nUser Experience", "home_feature_desc_2": "The GasFree model allows users to transfer assets without holding native tokens (e.g., TRX) or paying gas fees. Users simply sign transfer requests off-chain, while the service provider covers the gas costs, offering a seamless, gas-free experience.", "home_feature_title_3": "<span class=\"em\">Fast and Reliable</span>\nService", "home_feature_desc_3": "The GasFree model lets third-party providers cover gas fees. Users pay a small service fee in tokens like USDT, while the provider completes the transfer after receiving the off-chain signature. This reduces user burden and encourages fast, reliable service.", "home_supported": "Supported", "home_token": "Token", "home_more_token": "More tokens will be supported", "home_work_mechanism": "How It Works", "home_work_mechanism_desc": "The GasFree model relies on the GasFreeFactory contract to generate unique addresses and uses off-chain signing and service provider proxies to complete transfers, enabling efficient gas-free virtual asset transfers. The core of the GasFree mechanism is built on users' off-chain authorization, provider proxy execution, and the contract's efficient verification and execution.", "home_partner": "Partner", "home_whitepaper": "White Paper", "home_partner_tronlink": "It is created by outstanding community developers of TRON and has established in-depth cooperation with a number of world-class wallets.Trusted by over 10,000,000 users worldwide", "withdraw_title": "Withdraw <PERSON>ken", "withdraw_go_transfer": "GasFree Transfer", "withdraw_token_address": "Token", "withdraw_amount": "Amount", "withdraw_balance": "Balance: ", "withdraw_max_btn": "Max", "withdraw_receive_address": "Recipient Address", "withdraw_withdraw_btn": "Withdraw", "withdraw_modal_completed": "Withdrawal Completed", "withdraw_modal_broadcast": "Transaction Broadcasted", "withdraw_modal_tronscan": "Please Check on Blockchain Explorer >", "withdraw_modal_ok": "Confirm", "withdraw_modal_cancel": "Cancel", "withdraw_modal_confirm_withdraw": "Confirm <PERSON>", "withdraw_modal_token": "Token", "withdraw_modal_withdraw_amount": "<PERSON><PERSON><PERSON> Amount", "withdraw_modal_waiting_signature": "Waiting for Signature", "withdraw_modal_withdraw_btn": "Withdraw", "withdraw_modal_reserved": "Withdrawal will consume some TRX, it is recommended to reserve TRX as Gas fee", "withdraw_select_token": "Please Select Token", "withdraw_enter_token_id": "Please Enter Token ID", "withdraw_enter_receive_address": "Please Enter Recipient Address", "withdraw_tip0": "GasFree account now supports gas-free transfer of <span class=\"em\">USDT</span>, more tokens will be supported in the future;", "withdraw_tip1": "If a GasFree account receives unsupported tokens, you can withdraw them to your normal address on this page.", "withdraw_gasfree_intro_title": "GasFree", "withdraw_gasfree_intro_text": "The GasFree model provides a seamless transfer experience, eliminating the need for native tokens (e.g., TRX) to pay gas for transaction. By simplifying on-chain operations, it allows users to focus on asset management and transactions, improving the efficiency of digital asset use.", "withdraw_gasfree_intro_title2": "GasFree", "withdraw_gasfree_intro_text2": "The GasFree account currently supports GasFree transfer of USDT. If unsupported tokens are received in the GasFree account, you can withdraw the tokens to a normal address on the withdrawal page. GasFree will support more tokens in subsequent versions, stay tuned.", "withdraw_tronlink_transfer1": "Please go to TronLink wallet for GasFree transfer", "withdraw_tronlink_transfer2": "If TronLink is not installed, please go to <a href=\"https://www.tronlink.org/\" target=\"_blank\">TronLink Official Website</a> for installation", "withdraw_ok": "I Understand", "withdraw_no_asset": "No Withdrawable Assets", "withdraw_search_token": "Please Enter <PERSON> Address to Search", "transfer_gasfree_send": "GasFree Transfer", "transfer_send": "Send", "transfer_setting": "Advanced Settings", "transfer_not_activited": "Account Not Activated", "transfer_fee": "Transfer Fee", "transfer_income": "Amount Received", "transfer_transfer_token": "Token Transfer", "transfer_search_token_name": "Input the token name to search", "transfer_search_no_result": "Token not found", "transfer_fresh": "Fresh", "transfer_advanced_setting": "Advanced Setting", "transfer_deadline": "Deadline", "transfer_deadline_min_error": "Deadline must be at least 1 minute", "transfer_confirm_title": "Confirm Transaction", "transfer_waiting_confirm_title": "Waiting for Transaction Confirmation", "transfer_total_transfer_out": "Total Transfer Out", "transfer_go_approve": "Go to Approve", "transfer_waiting_approving": "Waiting for <PERSON><PERSON><PERSON><PERSON>", "transfer_ok": "OK", "transfer_error_approve_cancel": "You cancelled the approval, you can restart", "transfer_error_approve_failed": "Approval failed, please try again", "transfer_waiting_result": "Waiting for approval result", "transfer_approve_submited": "A<PERSON>roval submitted", "transfer_approve_submited_desc": "Submitted to GasFree service provider, the provider will submit to blockchain soon. You can check progress in the 'Activity' page", "transfer_record": "Transaction Record", "transfer_status": "Status", "transfer_datetime": "Time", "transfer_amount": "Amount", "transfer_send_address": "Sender Address", "transfer_tx_hash": "Transaction Hash", "transfer_error_balance": "Insufficient Balance", "transfer_error_invalid_address": "Invalid Recipient Address Format", "transfer_error_same_address": "Recipient address cannot be the same as user address", "transfer_error_forbid_transfer": "Current account cannot make transfers", "transfer_error_submit_ProviderAddressNotMatchException": "Provider address does not match", "transfer_error_submit_DeadlineExceededException": "Deadline exceeded", "transfer_error_submit_InvalidSignatureException": "Invalid signature", "transfer_error_submit_UnsupportedTokenException": "This token transfer is not supported", "transfer_error_submit_TooManyPendingTransferException": "Too many pending transfers waiting for on-chain confirmation", "transfer_error_submit_VersionNotSupportedException": "Offline signature version not supported", "transfer_error_submit_NonceNotMatchException": "Transfer nonce does not match", "transfer_error_submit_MaxFeeExceededException": "Estimated fee exceeds maximum fee limit", "transfer_error_submit_InsufficientBalanceException": "Insufficient Balance", "transfer_error_setting_deadline_min": "Deadline must not be less than {seconds} seconds", "transfer_error_setting_deadline_max": "Deadline must not exceed {seconds} seconds", "transfer_transaction_detail": "Transaction Details", "transfer_transaction_detail_hash": "Hash", "transfer_transaction_detail_result": "Result", "transfer_transaction_detail_type": "Transaction Type", "transfer_transaction_detail_nonce": "<PERSON><PERSON>", "transfer_transaction_detail_permitTime": "Authorization Time", "transfer_transaction_detail_onChainTime": "On-Chain Time", "transfer_transaction_detail_endTime": "Deadline", "transfer_transaction_detail_fee": "Fee", "transfer_transaction_detail_amount": "Amount Received", "transfer_transaction_detail_succeed": "Transaction Completed", "transfer_transaction_detail_inprogress": "Waiting for GasFree service provider to submit to blockchain", "transfer_transaction_detail_failed": "This authorization has expired", "transfer_transaction_detail_toBrowser": "View in Browser", "footer_docs": "Documents", "top_nile_reminder": "The GasFree address on Nile testnet is different from other chains. Do not use this address to receive funds on other chains, especially not on mainnet.", "nile_faucet": "Faucet", "newhome_hero_title1": "Cost-Effective", "newhome_hero_title2": "Accessible Transfers", "newhome_hero_title3": "For Everyone", "newhome_hero_desc": "With a seamless transfer experience that requires no native tokens, GasFree empowers users to focus more on asset management itself", "newhome_hero_transfer": "GasFree Transfer", "newhome_hero_wallets": "GasFree Wallets", "newhome_hero_integrate": "Integrate Us", "newhome_hero_volume": "Transaction Volume", "newhome_hero_count": "Transaction Count", "newhome_hero_fees": "<PERSON><PERSON> Saved", "newhome_feat_title1": "Simple  <br /> &  <br />  Low-cost  <br /> Transfer Standard", "newhome_feat_desc1": "A cost-effective and easier way of making transfers, designed for mass adoption", "newhome_feat_feature_title1": "Cheaper Transfer", "newhome_feat_feature_desc1": "GasFree <span class=\"highlight\">reduces costs by up to 75%</span> compared to traditional transfers, and users only need to pay a small fee in the tokens they transfer.", "newhome_feat_feature_title2": "Easy to Use", "newhome_feat_feature_desc2": "By removing the need for native tokens for transfers or activation of an account before it can be used, GasFree lowers the barrier to the crypto world.", "newhome_feat_feature_title3": "Fully Decentralized", "newhome_feat_feature_desc3": "GasFree enhances security by enabling transfers in a simpler and fully decentralized way, minimizing the risk of exposing sensitive information.", "newhome_feat_feature_title4": "Smoother Experience", "newhome_feat_feature_desc4": "GasFree eliminates the burden of paying gas fees. Users simply sign transfers and all the rest is handled with good care, offering a seamless experience.", "newhome_ecosys_title": "Ecosystem built <br /> for growth", "newhome_ecosys_desc": "Global network of wallet applications and DApps all interconnected", "newhome_ecosys_user_base": "User Base", "newhome_community_title": "Better transfers powered by a thriving community", "newhome_community_desc": "An international movement unites enthusiasts to shape a better future", "newhome_community_comment_julien": "The GasFree is incomparable - it has a good mechanism and is user-friendly. The developers deserve an accolade for its smoothness. This feature makes it possible for me to transfer a lot of crypto in seconds without paying the gas. Wow", "newhome_community_comment_freja": "Never did I think one day we could get rid of gas fee for crypto transfers. It's a wonderful experience that has reignited my passion for crypto", "newhome_community_comment_grazhio": "Who knew gasless crypto transfer could feel this revolutionary? It's reignited my belief in blockchain's future.", "newhome_community_comment_llya": "This is a genuinely groundbreaking innovation. We can invite newcomers to the crypto world without having to clarify the reasons behind certain requirements, like gas fees, for completing transactions", "newhome_community_comment_muhammad": "It's a game-changing protocol that removes crypto's biggest onboarding hurdle - explaining gas fees - letting newcomers experience blockchain as simply as sending a text.", "newhome_community_comment_ayaka": "Managing crypto wallets and various accounts has become easier and hassle-free, thanks to this great revisiting of the basic concept of needing to pay gas fees", "newhome_future_title": "GasFree Experiences, And Beyond", "newhome_future_desc": "From a smoother user experience to more secure crypto transfers, GasFree offers all kinds of perks to wallet applications and DeFi websites for low-barrier integration. Whenever you are looking to advance to the next level, we've got what you need to expand your horizons.", "newhome_future_touch": "Get in Touch", "newhome_nav_products": "Products", "newhome_nav_gasfree_transfer": "GasFree Transfer", "newhome_nav_3rd_party": "3rd-Party Integrations", "newhome_nav_join": "Join", "newhome_nav_developers": "Developers", "newhome_nav_docs": "Documentation", "newhome_nav_dev_center": "Developer Center", "newhome_nav_github": "GitHub", "newhome_nav_faucet": "Faucets", "newhome_nav_resource": "Resources", "newhome_nav_whitepaper": "Whitepaper", "newhome_nav_help_center": "Help Center", "newhome_nav_faqs": "FAQs", "newhome_nav_contact": "Contacts", "newhome_nav_technical": "Technical Inquiry", "newhome_nav_feedback": "<PERSON><PERSON><PERSON>", "newhome_nav_community": "Community", "newhome_nav_x": "X", "newhome_nav_telegram": "Telegram", "newhome_header_discord": "Discord", "newhome_footer_products": "Products", "newhome_footer_gasfree_transfer": "GasFree Transfer", "newhome_footer_intergrations": "3rd-Party Integrations", "newhome_footer_join_integrate": "Join & Integrate", "newhome_footer_developers": "Developers", "newhome_footer_documentation": "Documentation", "newhome_footer_developer_center": "Developer Center", "newhome_footer_github": "GitHub", "newhome_footer_faucets": "Faucets", "newhome_footer_resources": "Resources", "newhome_footer_whitepaper": "Whitepaper", "newhome_footer_help_center": "Help Center", "newhome_footer_faqs": "FAQs", "newhome_footer_contacts": "Contacts", "newhome_footer_tech_inquiry": "Technical Inquiry", "newhome_footer_feedback": "<PERSON><PERSON><PERSON>", "newhome_footer_copyright": "Copyright© 2024-2025", "newhome_footer_privacy": "Privacy Policy", "newhome_footer_terms": "Terms of Service", "integrations_title": "3rd-Party Integrations", "integrations_subtitle": "Explore wallet apps with GasFree integration, offering enhanced, cost-effective digital asset transfers.", "integrations_description": "Check our curated wallet list for a streamlined transaction experience.", "integrations_cta_title": "Elevate your wallet application by supporting", "integrations_cta_highlight": "GasFree Experience", "integrations_cta_button": "Join & Integrate", "integrations_wallet_tronlink": "TronLink", "integrations_tronlink_desc": "Wallet app created by outstanding community developers of TRON and trusted by over 10,000,000 users worldwide.", "integrations_wallet_imtoken": "imToken", "integrations_imtoken_desc": "imToken is a reliable and intuitive digital wallet trusted by millions.", "integrations_wallet_guarda": "Guarda", "integrations_guarda_desc": "Guarda is an ultimate solution for modern crypto management. You can do it all from the comfort of one single app.", "integrations_wallet_klever": "Klever wallet", "integrations_klever_desc": "The ultimate all-in-one hot and cold wallet to store, send, and receive over 1400 cryptos securely across 40+ blockchains with ease and control.", "integrations_wallet_edir": "eDir", "integrations_edir_desc": "eDir is a tool developed by Bixi for managing and transferring funds across multiple wallets.", "newhome_faqs_title": "FAQs", "newhome_faqs_q1": "Do I really no longer have to pay the gas fee?", "newhome_faqs_a1": "Yes! With the GasFree service, you only have to pay a small fee in the token you want to transfer. No more hassle of paying gas fees.", "newhome_faqs_q2": "How is the GasFree transfer more secure?", "newhome_faqs_a2": "With the GasFree service, you don't have to expose your sensitive account information when performing transfers, thus protecting your privacy and making it more secure.", "newhome_faqs_q3": "How much does a GasFree transfer cost?", "newhome_faqs_a3": "Currently, a typical GasFree transfer costs no more than $ 1 when transferring some of the most frequently used tokens, making it significantly cheaper compared to other transaction methods.", "newhome_faqs_q4": "Can you reveal more details about how GasFree works?", "newhome_faqs_a4": "The GasFree service relies on a decentralized smart contract to generate unique addresses and uses off-chain signing and vendor proxies to complete transfers, enabling highly efficient gas-free virtual asset transfers. The core of the GasFree service is built on users' off-chain authorization, vendor proxy execution, and the smart contract's efficient verification and execution.\r\n\r\nTo summarize, GasFree enables hassle-free transfers using secure and decentralized smart contracts.", "newhome_hero_label_fee": "GasFree Transfer Fee:", "newhome_hero_label_fee_traditional": "Traditional Transfer Fee:", "newhome_nav_withdraw": "Withdraw <PERSON>ken", "newhome_per_Txn": "per Txn"}