.privacyContainer {
  min-height: 100vh;
  height: 100vh;
  background: #000;
  color: white;
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    right: 0;
    height: 280px;
    background: url('./images/togBg.svg');
    background-size: cover;
    background-position: center top;
    background-repeat: no-repeat;
    z-index: 0;
    pointer-events: none;
    width: 980px;
    height: 280px;
    margin-left: -490px;
  }
}

.main {
  margin-top: 200px;
  padding-bottom: 0;
  z-index: 1;
  min-height: calc(100vh - 200px);
  position: relative;
  background: url('../Integrations/images/footerBg.svg');
  background-size: auto;
  background-position: center bottom;
  background-repeat: no-repeat;
  min-height: 600px;
  padding-top: 200px;
}

.content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0;
  user-select: text;
  -webkit-user-select: text;
  position: relative;
  z-index: 1;
  margin-top: -250px;
}

.header {
  text-align: center;
  margin-bottom: 60px;
}

.title {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 16px 0;
  color: white;
  line-height: 24px;
}

.lastUpdated {
  font-size: 16px;
  line-height: 30px;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  font-weight: 400;
}

.section {
  margin-bottom: 48px;
}

.sectionTitle {
  font-size: 20px;
  font-weight: 700;
  color: white;
  margin: 0 0 24px 0;
  line-height: 24px;
  text-align: left;
}

.introText {
  font-size: 16px;
  line-height: 30px;
  color: rgba(255, 255, 255, 0.7);
  margin: 0 0 20px 0;
  text-align: left;
}

.text {
  font-size: 16px;
  line-height: 30px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0 0 16px 0;
  text-align: left;
}

.list {
  margin: 0 0 16px 0;
  padding-left: 24px;

  li {
    font-size: 16px;
    line-height: 30px;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 8px;
    text-align: left;
    padding-left: 20px;
    position: relative;

    &::before {
      content: '-';
      position: absolute;
      left: 0;
      color: rgba(255, 255, 255, 0.6);
    }

    &::marker {
      color: rgba(255, 255, 255, 0.6);
      display: none;
    }
  }
}

/* 移动端响应式设计 */
@media (max-width: 1024px) {
  .content {
    max-width: 95%;
    padding: 0 24px;
  }

  .footerSection {
    background-size: 1200px auto;
    background-position: center bottom;
    min-height: 500px;
  }
}

@media (max-width: 768px) {
  .privacyContainer {
    font-size: 16px;
  }

  .content {
    padding: 0 20px;
  }

  .main {
    padding-top: 80px;
    padding-bottom: 30px;
    min-height: calc(100vh - 80px);
  }

  .footerSection {
    background-size: 1000px auto;
    background-position: center bottom;
    min-height: 450px;
    margin-top: -30px;
    padding-top: 30px;
  }

  .header {
    margin-bottom: 40px;
  }

  .title {
    font-size: 28px;
    line-height: 1.2;
    margin-bottom: 12px;
  }

  .lastUpdated {
    font-size: 14px;
    line-height: 24px;
  }

  .section {
    margin-bottom: 32px;
  }

  .sectionTitle {
    font-size: 18px;
    line-height: 1.3;
    margin-bottom: 16px;
  }

  .introText,
  .text {
    font-size: 14px;
    line-height: 24px;
    margin-bottom: 16px;
  }

  .list {
    padding-left: 16px;

    li {
      font-size: 14px;
      line-height: 24px;
      padding-left: 16px;
      margin-bottom: 6px;
    }
  }
}

@media (max-width: 480px) {
  .content {
    padding: 0 16px;
  }

  .main {
    padding-top: 70px;
    padding-bottom: 20px;
    min-height: calc(100vh - 70px);
  }

  .footerSection {
    background-size: 800px auto;
    min-height: 400px;
    margin-top: -20px;
    padding-top: 20px;
  }

  .header {
    margin-bottom: 32px;
  }

  .title {
    font-size: 24px;
    line-height: 1.2;
    margin-bottom: 10px;
  }

  .lastUpdated {
    font-size: 13px;
    line-height: 20px;
  }

  .section {
    margin-bottom: 28px;
  }

  .sectionTitle {
    font-size: 16px;
    line-height: 1.3;
    margin-bottom: 12px;
  }

  .introText,
  .text {
    font-size: 13px;
    line-height: 22px;
    margin-bottom: 12px;
  }

  .list {
    padding-left: 12px;

    li {
      font-size: 13px;
      line-height: 22px;
      padding-left: 12px;
      margin-bottom: 4px;
    }
  }
}

@media (max-width: 375px) {
  .content {
    padding: 0 12px;
  }

  .main {
    padding-top: 60px;
    padding-bottom: 15px;
  }

  .footerSection {
    background-size: 600px auto;
    min-height: 350px;
    margin-top: -15px;
    padding-top: 15px;
  }

  .header {
    margin-bottom: 24px;
  }

  .title {
    font-size: 22px;
    word-break: break-word;
  }

  .lastUpdated {
    font-size: 12px;
    line-height: 18px;
  }

  .section {
    margin-bottom: 24px;
  }

  .sectionTitle {
    font-size: 15px;
    margin-bottom: 10px;
    word-break: break-word;
  }

  .introText,
  .text {
    font-size: 12px;
    line-height: 20px;
    margin-bottom: 10px;
    word-break: break-word;
  }

  .list {
    padding-left: 8px;

    li {
      font-size: 12px;
      line-height: 20px;
      padding-left: 8px;
      margin-bottom: 3px;
      word-break: break-word;
    }
  }
}

/* 横屏模式优化 */
@media (max-width: 768px) and (orientation: landscape) {
  .main {
    padding-top: 60px;
    padding-bottom: 20px;
  }

  .footerSection {
    min-height: 300px;
    margin-top: -20px;
    padding-top: 20px;
  }

  .header {
    margin-bottom: 30px;
  }

  .title {
    font-size: 24px;
  }

  .section {
    margin-bottom: 24px;
  }
}
