<svg width="920" height="280" viewBox="0 0 920 280" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="920" height="280" fill="black"/>
<mask id="mask0_748_4003" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="920" height="280">
<rect width="920" height="280" fill="#D9D9D9"/>
</mask>
<g mask="url(#mask0_748_4003)">
<g filter="url(#filter0_f_748_4003)">
<ellipse cx="460.488" cy="-11.6411" rx="318.453" ry="126.5" transform="rotate(3.99125 460.488 -11.6411)" fill="url(#paint0_linear_748_4003)" fill-opacity="0.5"/>
</g>
<g filter="url(#filter1_f_748_4003)">
<ellipse cx="460.486" cy="-40.6411" rx="318.453" ry="126.5" transform="rotate(3.99125 460.486 -40.6411)" fill="url(#paint1_linear_748_4003)" fill-opacity="0.5"/>
</g>
</g>
<defs>
<filter id="filter0_f_748_4003" x="-57.3184" y="-339.788" width="1035.61" height="656.293" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="100" result="effect1_foregroundBlur_748_4003"/>
</filter>
<filter id="filter1_f_748_4003" x="-57.3203" y="-368.788" width="1035.61" height="656.293" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="100" result="effect1_foregroundBlur_748_4003"/>
</filter>
<linearGradient id="paint0_linear_748_4003" x1="653.308" y1="85.3588" x2="368.794" y2="74.0237" gradientUnits="userSpaceOnUse">
<stop stop-color="#1938FF"/>
<stop offset="1" stop-color="#28FF97"/>
</linearGradient>
<linearGradient id="paint1_linear_748_4003" x1="653.306" y1="56.3588" x2="368.792" y2="45.0237" gradientUnits="userSpaceOnUse">
<stop stop-color="#1938FF"/>
<stop offset="1" stop-color="#28FF97"/>
</linearGradient>
</defs>
</svg>
