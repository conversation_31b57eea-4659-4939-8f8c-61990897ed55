.withdrawPageWrap {
  // min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: #131121 url(./images/bg.png) no-repeat center top/auto;
  min-height: 1004px;
  .contentWrap {
    position: relative;
    padding-top: 100px;
    padding-bottom: 100px;
    flex: 1;
    // background: url(./images/bg.png) no-repeat center bottom/auto;
    // min-height: 1004px;
    // margin-bottom: -200px;
  }
}
.withdrawTitle {
  width: 580px;
  font-size: 32px;
  font-weight: 600;
  line-height: 45px;
  text-align: left;
  padding-left: 20px;
  color: #33ffa9;
  margin: auto;
  margin-bottom: 15px;
  position: relative;
  .transfer {
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    background-color: rgba(51, 255, 170, 0.1);
    right: 20px;
    bottom: -15px;
    padding: 0 20px;
    cursor: pointer;

    &:hover {
      background-color: rgba(51, 255, 170, 0.15);
    }

    :global {
      .text {
        font-size: 14px;
        font-weight: 600;
        line-height: 37px;
        color: #33ffa9;
      }
      .icon {
        display: block;
        width: 12px;
        height: 12px;
        background: url(./images/arrow-top-right.svg) no-repeat center/contain;
        margin-left: 5px;
      }
    }
  }
}
.withdrawWrap {
  width: 580px;
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  margin: 0 auto;
  position: relative;
  background: #242237;
  padding: 20px;
}
.withdrawWrapLine {
  background: url(./images/withdraw-wrap-bg.png) no-repeat center;
  background-size: 239px 9px;
  position: absolute;
  top: -6px;
  width: 239px;
  height: 9px;
  left: 20px;
}
.withdrawTokenWrap {
  width: 540px;
  height: 95px;
  border: 1px solid transparent;
  border-color: #1c1a30;
  padding: 15px;
  margin-bottom: 30px;
  border-radius: 10px;
  transition: border-color 0.3s;
  background-color: #1c1a30;
  &:hover {
    border-color: #33fea8;
  }
}
.withdrawTokenTitle {
  font-size: 14px;
  font-weight: 700;
  line-height: 20px;
  color: rgba(255, 255, 255, 0.6);
  text-align: left;
  margin-bottom: 10px;
}
.withdrawTokenInfo {
  width: 100%;

  .tokenAddressInput {
    background: transparent;
    border: none;
    outline: none;
    box-shadow: none;
    padding: 4px 11px;
    color: rgba(255, 255, 255, 1);
    pointer-events: none;

    &::placeholder {
      color: rgba(255, 255, 255, 0.25);
    }

    :global {
      .ant-input-clear-icon {
        display: inline-flex;
        align-items: center;
      }
    }
    input {
      color: rgba(255, 255, 255, 1) !important;
      line-height: 30px;
      font-size: 14px;
      padding: 0;
      height: 32px;
      font-weight: 400;
      border: none;
      outline: none;
      box-shadow: none;
      &::placeholder {
        color: rgba(255, 255, 255, 0.25);
      }
    }
  }

  .inputWrap {
    display: flex;
    margin-top: 12px;
    align-items: center;
    .select {
      height: 36px;
      width: 100%;
      // background-color: rgba(255, 255, 255, 0.1);
      border-radius: 4px;
      padding: 0px;
      cursor: pointer;
      display: flex;
      align-items: center;

      .tokenIcon {
        width: 24px;
        height: 24px;
        margin-right: 10px;
        display: block;
        background: none no-repeat center / contain;
      }
      .tokenName {
        font-size: 14px;
        line-height: 22px;
        font-weight: 600;
        color: white;
        margin-right: 14px;
        text-align: left;
      }

      // &:hover {
      //   background-color: rgba(255, 255, 255, 0.3);
      // }
    }
    .count-input,
    .address-input {
      margin-left: 10px;
      background-color: transparent;
      border: none;
      font-size: 20px;
      line-height: 30px;
      font-weight: 600;
      padding-left: 0;
      color: white;
      &::placeholder {
        color: rgba(255, 255, 255, 0.25);
      }
    }
    .address-input {
      font-size: 14px;
      margin-left: 0;
    }
  }
}
.withdrawAmountWrap {
  width: 540px;
  height: 95px;
  border: 1px solid transparent;
  border-color: #1c1a30;
  display: flex;
  padding: 15px;
  border-radius: 10px;
  margin-bottom: 30px;
  flex-direction: column;
  position: relative;
  transition: border-color 0.3s;
  background-color: #1c1a30;

  // &:hover {
  //   border-color: #33fea8;
  // }
  .withdrawAmount {
    display: flex;
    // width: 500px;
    position: relative;
    margin-top: 15px;
  }

  .withdrawAmountInput {
    font-family: Inter;
    background: transparent;
    border: 1px solid transparent;
    color: rgba(255, 255, 255, 1);
    line-height: 38px;
    font-size: 20px;
    padding: 0;
    height: 30px;
    line-height: 30px;
    font-weight: 600;
    width: 440px;
    padding: 0;

    &:hover,
    &:focus,
    &:active {
      border: 1px solid transparent;
      outline: none;
      box-shadow: none;
    }
  }
  .withdrawAddressInput {
    height: 40px;
    width: 520px;
    outline: none;
    box-shadow: none;
    background: transparent;
  }

  .maxBtn {
    padding: 0 8px;
    background: rgba(255, 255, 255, 0.1);
    color: rgba(51, 255, 169, 1);
    height: 20px;
    line-height: 20px;
    position: absolute;
    top: 10px;
    right: 0;
    cursor: pointer;
    border-radius: 4px;
    font-weight: 500;
  }
}

.withdrawAmountTop {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;
  line-height: 20px;
}
.withdrawAmountTitle {
  font-size: 14px;
  font-weight: 700;
  color: rgba(255, 255, 255, 0.6);
}
.withdrawAmountBalance {
  font-size: 14px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.7);
}
.withdrawReceivetWrap {
  width: 540px;
  height: 90px;
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 10px;
  padding: 16px;
  margin-bottom: 20px;
}
.withdrawReceivetAddress {
  font-family: Inter;
  font-size: 14px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.6);
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-bottom: 10px;

  span:nth-child(1) {
    background: url(./images/receive-address.svg) no-repeat left center;
    padding-left: 20px;
  }
}

.withdrawReceivetAmount {
  font-family: Inter;
  font-size: 14px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.6);
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;

  span:nth-child(1) {
    background: url(./images/receive-amount.svg) no-repeat left center;
    padding-left: 20px;
  }
}

.tipsWrap {
  margin-top: 30px;
  margin-bottom: 15px;
  padding: 15px 17px 15px;
  border-radius: 10px;
  background-color: #2d2b41;
  :global {
    .tip + .tip {
      margin-top: 10px;
    }
    .tip {
      padding-left: 15px;
      position: relative;
      text-align: left;

      .decoration {
        position: absolute;
        display: block;
        width: 5px;
        height: 5px;
        border-radius: 100%;
        background-color: #fff;
        left: 0;
        top: 8px;
        bottom: 0;
        margin: 0;
      }
      span {
        font-size: 14px;
        line-height: 18px;
        color: rgba(255, 255, 255, 0.7);
      }
      .em {
        color: white;
        font-weight: 600;
      }
    }
  }
}

.connectWallet {
  width: 540px;
  height: 48px;
  cursor: pointer;
  background-color: #33ffa9;
  border-radius: 10px;
  font-size: 16px;
  font-weight: 600;
  color: #131211;
  text-align: center;
  line-height: 48px;
  transition: background-color 0.3s;

  &:hover {
    background-color: #27bd7e;
  }

  &.disabled {
    background: #4f4c6c;
    color: #fff;
  }
}

.introWrap {
  position: relative;
  padding-top: 20px;
  width: 346px;
  margin-left: 100%;
  margin-top: -190px;
  transform: translateX(-110%);
  z-index: 100;
  &.hide {
    display: none;
  }
  :global {
    .icon {
      position: absolute;
      left: 24px;
      top: 0;
      width: 40px;
      height: 40px;
      background: url(./images/gasfree-logo.svg) no-repeat center/contain;
      z-index: 2;
    }
    .intro {
      border-radius: 20px;
      background-color: #242237;
      padding: 40px 25px;
      position: relative;
      text-align: left;
      .title span {
        font-size: 24px;
        font-weight: 800;
        line-height: 30px;
        color: #33ffa9;
        white-space: pre-line;
        text-align: left;
      }
      .text {
        margin-top: 20px;
        font-size: 14px;
        line-height: 22px;
        color: #ffffff;
        text-align: left;
      }
    }
    .slick-slider {
      border-radius: 20px !important;
      background-color: #242237;

      > div {
        border-radius: 20px !important;
        background-color: #242237;
      }
    }
    .slick-dots-bottom {
      li {
        width: 48px !important;
        background: rgba(255, 255, 255, 0.15);
        border-radius: 10px !important;

        button {
          background: rgba(255, 255, 255, 0.15);
          border-radius: 10px !important;
        }

        &:hover,
        &.slick-active {
          background: #33ffa9;
          border-radius: 10px !important;

          button {
            background: #33ffa9;
            border-radius: 10px !important;
          }
        }
      }
    }
  }
}

@media screen and (max-width: 750px), screen and (orientation: landscape) and (max-width: 1024px) {
  .withdrawPageWrap {
    min-height: unset;
    background: none;
  }
  .withdrawWrap {
    width: 100%;
    padding: 12px;
    min-height: unset;
  }
  .withdrawTitle {
    width: unset;
    padding-left: 30px;
    font-size: 18px;
    line-height: 25px;
    margin-bottom: 10px;

    .transfer {
      border-top-left-radius: 10px;
      border-top-right-radius: 10px;
      background-color: rgb(51, 255, 169, 0.1);
      bottom: -10px;
      padding: 8px;
      right: 15px;
      :global {
        .text {
          font-size: 10px;
          line-height: 12px;
        }
        .icon {
          width: 10px;
          height: 10px;
          background-image: url(./images/m/arrow-top-right.svg);
        }
      }
    }
  }
  .withdrawTokenInfo {
    .tokenName {
      margin-left: 5px;
    }
  }
  .withdrawWrapLine {
    width: 100%;
    left: 0;
    margin-left: 0;
    background-position: left center;
  }
  .withdrawTokenWrap {
    width: 100%;
  }
  .withdrawAmountWrap {
    width: 100%;
  }
  .withdrawAmount {
    width: calc(100% - 16px);
  }
  .withdrawReceivetWrap {
    width: 100%;
    position: relative;
  }
  .withdrawAmountInput {
    width: 100%;
    max-width: 300px;
  }
  .connectWallet {
    width: 100%;
  }
  .tipsWrap {
    padding: 10px 15px;
    :global {
      .tip span {
        font-size: 12px;
        line-height: 18px;
        .decoration {
          top: 8px;
        }
      }
    }
  }
  .introWrap {
    position: relative;
    width: 100%;
    left: 0;
    bottom: 20px;
    margin-top: 30px;
  }

  .withdrawPageWrap {
    .contentWrap {
      padding: 0 15px;
      margin-top: 60px;
      background: #131211;
      min-height: unset;
      margin-bottom: 0;
    }
  }
}
