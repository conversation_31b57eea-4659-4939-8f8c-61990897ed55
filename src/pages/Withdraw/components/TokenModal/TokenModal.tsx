import { Modal } from '@/components/Modal/Modal';
import intl from 'react-intl-universal';
import styles from './TokenModal.module.scss';
import { Input } from '@/components/wraped-antd/Input/Input';
import SearchIcon from '../../images/search.svg';
import classNames from 'classnames';
import { ChangeEvent, useMemo, useState } from 'react';
import { Asset } from '../../service';
import { getTokenImg, getTokenName, getTokenShortName, handleImageError, getTokenType } from '../../utils';
import { formatMobileAddress, formatNumber } from '../../../../utils/helper';
import { useIsMobile } from '../../../../hooks/useIsMobile';
import BigNumber from 'bignumber.js';

type Props = {
  tokenList: Array<Asset>;
  visible: boolean;
  selectedToken: Asset | null;
  onSelect: (token: Asset) => unknown;
  onClose: () => unknown;
  fetchTokenList: (tokenAddress: string) => unknown;
};

export function TokenModal(props: Props) {
  const [filter, setFilter] = useState('');
  const list = useMemo(() => {
    return filter
      ? props.tokenList.filter(token => token.tokenSymbol?.toLowerCase().includes(filter.toLowerCase()))
      : props.tokenList;
  }, [props, filter]);
  // function onInputChange(e: ChangeEvent<HTMLInputElement>) {
  //   setFilter(e.target.value);
  // }

  const isMobile = useIsMobile();

  const NotFound = useMemo(
    () => (
      <div className={styles.notFound}>
        <div className="icon"></div>
        <div className="text">{intl.get('withdraw_no_asset')}</div>
      </div>
    ),
    []
  );

  return (
    <Modal
      onClose={() => {
        props.onClose();
        window.gtag && window.gtag('event', 'withdraw_tokenwindow_close');
      }}
      className={styles.modal}
      visible={props.visible}
      title={intl.get('withdraw_title')}
    >
      <div className={styles.tokenWrap}>
        <Input
          onChange={e => {
            props.fetchTokenList(e.target.value);
            if (!e.target.value) window.gtag && window.gtag('event', 'withdraw_tokenwindow_clear');
          }}
          allowClear
          className={styles.search}
          prefix={<img src={SearchIcon} />}
          placeholder={intl.get('withdraw_search_token')}
        />
        <div className={styles.divider}></div>
        <div className={styles.listWrap}>
          {list?.length > 0
            ? list.map(token => (
                <div
                  key={token.shortName}
                  className={classNames(
                    styles.tokenItem,
                    props.selectedToken?.type === token?.type &&
                      props.selectedToken?.id === token?.id &&
                      props.selectedToken?.contractAddress === token.contractAddress &&
                      styles.selected
                  )}
                  onClick={() => {
                    props.onSelect(token);
                    window.gtag && window.gtag('event', 'withdraw_tokenwindow_selecttoken');
                  }}
                >
                  <div className={`icon token-icon`}>
                    <img src={getTokenImg(token)} width={40} height={40} onError={handleImageError} />
                  </div>
                  <div className="nameWrap flex column">
                    <span className="abbr">
                      {getTokenShortName(token)}
                      {getTokenType(token) && <span className="type">{getTokenType(token)}</span>}
                    </span>
                    <span className="name">{getTokenName(token)}</span>
                  </div>
                  <div className="info flex column items-end">
                    <span className="balance">{formatNumber(BigNumber(token.balanceStr), 6)}</span>
                    <span className="address">
                      {isMobile ? formatMobileAddress(token.contractAddress) : token.contractAddress}
                    </span>
                  </div>
                </div>
              ))
            : NotFound}
        </div>
      </div>
    </Modal>
  );
}
