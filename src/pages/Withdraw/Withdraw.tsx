import { useEffect } from 'react';
import { observer } from 'mobx-react-lite';

import Config from '../../config';
import { useNavigate } from 'react-router-dom';
import { useChainInfo } from '../../hooks/useChainInfo';
import { isTron } from '@/utils/chains';
import WithdrawEVM from './WithdrawEVM';
import WithdrawTron from './WithdrawTron';

const Withdraw = observer(() => {
  const { chainId } = useChainInfo();

  const navigate = useNavigate();

  useEffect(() => {
    window.gtag && window.gtag('event', 'withdraw_load');
    if (!Config.support_chain.includes(chainId)) {
      navigate('/unsupport');
    }
  }, []);

  return <>{isTron(chainId) ? <WithdrawTron /> : <WithdrawEVM />}</>;
});

export default Withdraw;
