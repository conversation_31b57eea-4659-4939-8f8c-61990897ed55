import { useEffect, useState } from 'react';
import { observer } from 'mobx-react-lite';
import styles from './withdraw.module.scss';
import { Input, Select, Carousel } from 'antd';
import { triggerSmartContract, sendRawTransaction, getTrxBalance, getTokenInfo } from '../../utils/blockchain';
import { formatNumber, formatMobileAddress } from '../../utils/helper';
import { useWallet } from '../../components/WalletProvider/useWallet';
import BigNumber from 'bignumber.js';
import intl from 'react-intl-universal';

import Config from '../../config';
import { useSearchParams } from 'react-router-dom';
import { useIsMobile } from '../../hooks/useIsMobile';

import TRXImg from './images/trx.png';
import { useStores } from '../../stores';
import WithdrawModal from './WithdrawModal';
import { TronWeb } from 'tronweb';
import { getTokenAssetInfo, getTokenAssetList } from '../../utils/asset';
import { SelectArrowDownLarge } from '../../components/icons/SelectArrowDownLarge';
import { PageContainer } from '../../components/PageContainer/PageContainer';
import { useChainInfo } from '../../hooks/useChainInfo';
import { TokenModal } from './components/TokenModal/TokenModal';
import { getTokenImg, getTokenShortName, handleImageError, isTokenInList } from './utils';
import { CloseButton } from '@/components/CloseButton.tsx';
import classNames from 'classnames';
import { TransferTipModal } from './components/TokenModal/TransferTipModal/TransferTipModal';
import DefaultTokenImg from './images/defaultTokenImg.svg';
import { getTokenList } from './service';

interface Token {
  name: string;
  shortName: string;
  decimals: number;
  logoUrl: string;
  contractAddress: string;
  balanceStr: string;
  type: number;
  id: string;
}

const WithdrawTron = observer(() => {
  const { address, signTransaction, showConnectModal, switchChainIfNeed } = useWallet();
  // const [gasFreeAddress, setGasFreeAddress] = useState(false);

  const { chainId } = useChainInfo();
  const isMobile = useIsMobile();

  const [deployeGasFreeAddress, setDeployeGasFreeAddress] = useState(false);
  const [gasFreeBalance, setGasFreeBalance] = useState(BigNumber(0));
  const [isDisabled, setIsDisabled] = useState(true);
  const [modalVisible, setModalVisible] = useState(false);
  const [txId, setTxId] = useState('');
  const [token, setToken] = useState<Token>({
    name: 'TRX',
    shortName: 'TRX',
    decimals: 6,
    logoUrl: TRXImg,
    contractAddress: '',
    balanceStr: '0',
    type: 0,
    id: ''
  });

  const trc10Regex = /^1\d{6}$/;

  const { userTron } = useStores();

  const [searchParams] = useSearchParams();
  const [tokenModalVisible, setTokenModalVisible] = useState(false);
  const [tokenList, setTokenList] = useState([]);
  const [allTokens, setAllTokens] = useState([]);
  const [modalLoading, setModalLoading] = useState(false);
  const env = import.meta.env.VITE_APP_ENV || '';
  const onSelectToken = function (token: Token) {
    setToken(token);
    setTokenModalVisible(false);
  };

  useEffect(() => {
    const tokenAddress = searchParams.get('20token');
    const tokenId = searchParams.get('10token');
    if (tokenAddress) {
      setToken({
        contractAddress: tokenAddress,
        name: '',
        shortName: '',
        decimals: 18,
        logoUrl: DefaultTokenImg,
        balanceStr: '0',
        type: 2,
        id: ''
      });
    } else if (tokenId) {
      setToken({
        contractAddress: '',
        name: '',
        shortName: '',
        decimals: 18,
        logoUrl: DefaultTokenImg,
        balanceStr: '0',
        type: 1,
        id: tokenId
      });
    } else if (tokenList.length > 0) {
      setToken(tokenList[0]);
    }
  }, [searchParams]);

  // useEffect(() => {
  //   async function fetchData() {
  //     await switchChainIfNeed();
  //   }
  //   fetchData();
  // }, []);

  //判断Disabled状态
  useEffect(() => {
    setIsDisabled(true);
    if (userTron.deployedGasFrees && userTron.deployedGasFrees === Config.contract[chainId].holeAddress) {
      setIsDisabled(true);
      return;
    }

    if (isTokenInList(allTokens, token.contractAddress)) {
      setIsDisabled(true);
      return;
    }

    if (Number(token.balanceStr) > 0) {
      setIsDisabled(false);
    } else {
      setIsDisabled(true);
    }
  }, [userTron.deployedGasFrees, token.type, token.id, token.contractAddress, token.balanceStr]);

  useEffect(() => {
    if (address) {
      // getTokenList().then(res => {
      //   setAllTokens(res);
      // });
      setAllTokens(Config.contract[chainId].unSupportTokens);
    }
  }, [address]);

  // 获取已经激活的gasFree地址
  useEffect(() => {
    if (address) {
      async function fetchDeployedGasFree() {
        await userTron.getUserDeployedGasFrees(address);
      }
      fetchDeployedGasFree();
    }
  }, [address]);

  async function fetchTokenInfo() {
    if (token.type === 0) {
      if (userTron.deployedGasFrees && userTron.deployedGasFrees !== Config.contract[chainId].holeAddress) {
        const tokenInfo = await getTokenAssetInfo(userTron.deployedGasFrees, 0, '');
        if (tokenInfo.success) {
          setToken(tokenInfo.data);
        }
      }
    } else if (token.type === 1) {
      if (userTron.deployedGasFrees && userTron.deployedGasFrees !== Config.contract[chainId].holeAddress) {
        if (token.id !== '' && trc10Regex.test(token.id)) {
          const tokenInfo = await getTokenAssetInfo(userTron.deployedGasFrees, 1, token.id);
          if (tokenInfo.success) {
            setToken(tokenInfo.data);
          }
        }
      }
    } else if (token.type === 2) {
      if (userTron.deployedGasFrees && userTron.deployedGasFrees !== Config.contract[chainId].holeAddress) {
        const tokenInfo = await getTokenAssetInfo(userTron.deployedGasFrees, 2, token.contractAddress);
        if (tokenInfo.success) {
          setToken(tokenInfo.data);
        } else {
          const tokenResult = await getTokenInfo(token.contractAddress, address, Config.contract[chainId].factory);
          // console.log('tokenResult');
          // console.log(tokenResult);
          if (tokenResult.success) {
            const tokenResultPrecision = BigNumber(10).pow(tokenResult.data.decimal.toString());
            setToken({
              balanceStr: BigNumber(tokenResult.data.balance).div(tokenResultPrecision).toString(),
              name: tokenResult.data.symbol,
              shortName: tokenResult.data.symbol,
              decimals: tokenResult.data.decimal.toString(),
              logoUrl: DefaultTokenImg,
              contractAddress: token.contractAddress,
              type: 2,
              id: ''
            });
          }
        }
      }
    }
  }

  async function fetchTokenList(tokenAddress: string) {
    if (userTron.deployedGasFrees && userTron.deployedGasFrees !== Config.contract[chainId].holeAddress) {
      if (tokenAddress !== '' && trc10Regex.test(tokenAddress)) {
        const tokenInfo = await getTokenAssetInfo(userTron.deployedGasFrees, 1, tokenAddress);
        if (tokenInfo.success) {
          setTokenList([tokenInfo.data]);
        } else {
          const tokenResult = await getTokenInfo(tokenAddress, address, Config.contract[chainId].factory);

          if (tokenResult.success) {
            const tokenResultPrecision = BigNumber(10).pow(tokenResult.data.decimal.toString());
            setTokenList([
              {
                balanceStr: BigNumber(tokenResult.data.balance).div(tokenResultPrecision).toString(),
                name: tokenResult.data.symbol,
                shortName: tokenResult.data.symbol,
                decimals: tokenResult.data.decimal.toString(),
                logoUrl: DefaultTokenImg,
                contractAddress: '',
                type: 1,
                id: tokenAddress
              }
            ]);
          }
        }
      } else if (tokenAddress !== '' && TronWeb.isAddress(tokenAddress)) {
        const tokenInfo = await getTokenAssetInfo(userTron.deployedGasFrees, 2, tokenAddress);
        if (tokenInfo.success) {
          setToken(tokenInfo.data);
        } else {
          const tokenResult = await getTokenInfo(tokenAddress, address, Config.contract[chainId].factory);
          if (tokenResult.success) {
            const tokenResultPrecision = BigNumber(10).pow(tokenResult.data.decimal.toString());
            setTokenList([
              {
                balanceStr: BigNumber(tokenResult.data.balance).div(tokenResultPrecision).toString(),
                name: tokenResult.data.symbol,
                shortName: tokenResult.data.symbol,
                decimals: tokenResult.data.decimal.toString(),
                logoUrl: DefaultTokenImg,
                contractAddress: tokenAddress,
                type: 2,
                id: ''
              }
            ]);
          }
        }
      }
    }
  }

  useEffect(() => {
    async function fetchData() {
      if (userTron.deployedGasFrees && userTron.deployedGasFrees != Config.contract[chainId].holeAddress) {
        const res = await getTokenAssetList(userTron.deployedGasFrees);
        const tokenList = res?.data?.token.filter(item => BigNumber(item.balanceStr).gt(1e-18));
        setTokenList(tokenList);
      }
    }
    fetchData();
  }, [userTron.deployedGasFrees, chainId, tokenModalVisible]);

  useEffect(() => {
    fetchTokenInfo();
  }, [userTron.deployedGasFrees, token.contractAddress, token.type, token.id]);

  useEffect(() => {
    if (address) {
      if (token.type === 0) {
        async function fetchData() {
          await userTron.getUserGasFreeAddress(address);
        }
        fetchData();
        async function fetchDeployedGasFree() {
          await userTron.getUserDeployedGasFrees(address);
        }
        fetchDeployedGasFree();
        // getTrxBalance(address).then(({ balance, success }) => {
        //   if (success) {
        //     setBalance(balance);
        //   }
        // });
      }
    }
  }, [address]);

  useEffect(() => {
    async function fetchData() {
      await userTron.getUserGasFreeAddress(address);
    }
    fetchData();
  }, [address]);

  const deployGasFreeAddress = async () => {
    try {
      const transaction = await triggerSmartContract(
        Config.contract[chainId].factory,
        'deployGasFree(address)',
        {},
        [{ type: 'address', value: address }],
        address,
        false
      );
      console.log('transaction: ', transaction);
      const signedTransaction = await signTransaction(transaction.transaction);
      const result = await sendRawTransaction(signedTransaction);
      console.log(result);
    } catch (err) {
      console.log('err', err);
    }
  };

  const retrieveTRX = async () => {
    try {
      const transaction = await triggerSmartContract(
        userTron.deployedGasFrees,
        'retrieveEther()',
        {},
        [],
        address,
        false
      );
      // console.log('transaction: ', transaction);
      const signedTransaction = await signTransaction(transaction.transaction);
      const { txid } = await sendRawTransaction(signedTransaction);
      setTxId(txid);
      setTimeout(fetchTokenInfo, 10000);
    } catch (err) {
      console.log('err', err);
      setModalLoading(false);
    }
  };

  const retrieveTRC10 = async (tokenAddress: string) => {
    try {
      const transaction = await triggerSmartContract(
        userTron.deployedGasFrees,
        'retrieveTRC10(trcToken)',
        {},
        [{ type: 'trcToken', value: tokenAddress }],
        address,
        false
      );
      console.log('transaction: ', transaction);
      const signedTransaction = await signTransaction(transaction.transaction);
      const { txid } = await sendRawTransaction(signedTransaction);
      setTxId(txid);
      setTimeout(fetchTokenInfo, 10000);
    } catch (err) {
      console.log('err', err);
      setModalLoading(false);
    }
  };

  const retrieveTRC20 = async (tokenAddress: string) => {
    try {
      const transaction = await triggerSmartContract(
        userTron.deployedGasFrees,
        'retrieve(address)',
        {},
        [{ type: 'address', value: tokenAddress }],
        address,
        false
      );
      // console.log('transaction: ', transaction);
      const signedTransaction = await signTransaction(transaction.transaction);
      const { txid } = await sendRawTransaction(signedTransaction);
      setTxId(txid);
      setTimeout(fetchTokenInfo, 10000);
    } catch (err) {
      console.log('err', err);
      setModalLoading(false);
    }
  };

  const confirm = async () => {
    try {
      await switchChainIfNeed();
      if (token.type === 0) {
        await retrieveTRX();
      } else if (token.type === 1) {
        await retrieveTRC10(token.id);
      } else if (token.type === 2) {
        await retrieveTRC20(token.contractAddress);
      }
    } catch (err) {
      console.log('err', err);
      setModalLoading(false);
    }
  };

  return (
    <PageContainer className={styles.withdrawPageWrap}>
      <div className={styles.contentWrap}>
        <WithdrawTitle />
        <div className={styles.withdrawWrap}>
          <div className={styles.withdrawWrapLine}></div>
          <div className={styles.withdrawTokenWrap}>
            <div className={styles.withdrawTokenTitle}>{intl.get('withdraw_token_address')}</div>
            <div className={styles.withdrawTokenInfo} id="tokenInfo">
              <div
                className={styles.inputWrap}
                onClick={() => {
                  if (address) {
                    setTokenModalVisible(true);
                    window.gtag && window.gtag('event', 'withdraw_body_token_connected');
                  } else {
                    showConnectModal();
                    window.gtag && window.gtag('event', 'withdraw_body_token_notconnected');
                  }
                }}
              >
                <div className={styles.select}>
                  <span className={styles.tokenIcon}>
                    <img src={getTokenImg(token)} width="24" height="24" onError={handleImageError} />
                  </span>
                  <span className={styles.tokenName}>{getTokenShortName(token)}</span>
                  <Input
                    placeholder={intl.get('withdraw_select_token')}
                    className={styles.tokenAddressInput}
                    value={
                      token?.type == 0
                        ? ''
                        : token?.type == 1
                          ? token?.id
                          : isMobile
                            ? formatMobileAddress(token?.contractAddress)
                            : token?.contractAddress
                    }
                    disabled={true}
                  />
                  <SelectArrowDownLarge />
                </div>
              </div>
            </div>
          </div>
          <div className={`${styles.withdrawAmountWrap} ${styles.middle}`}>
            <div className={styles.withdrawAmountTop}>
              <div className={styles.withdrawAmountTitle}>{intl.get('withdraw_amount')}</div>
              <div className={styles.withdrawAmountBalance}>
                {intl.get('withdraw_balance')} {formatNumber(BigNumber(token.balanceStr), 6)}
              </div>
            </div>
            <div className={styles.withdrawAmount}>
              <Input
                placeholder="0.00"
                className={styles.withdrawAmountInput}
                value={formatNumber(BigNumber(token.balanceStr), 6)}
                disabled={true}
              />
              {/* <div
                className={styles.maxBtn}
                onClick={() => {
                  setAmount(BigNumber(gasFreeBalance).toString());
                }}
              >
                {intl.get('withdraw_max_btn')}
              </div> */}
            </div>
          </div>
          {/* <div className={styles.withdrawAmountWrap}>
            <div className={styles.withdrawAmountTop}>
              <div className={styles.withdrawAmountTitle}>{intl.get('withdraw_receive_address')}</div>
            </div>
            <div className={styles.withdrawAmount}>
              <CustomInput
                placeholder={intl.get('withdraw_enter_receive_address')}
                className={styles.withdrawAddressInput}
                value={receiveAddress}
                onChange={e => setReceiveAddress(e.target.value)}
              />
            </div>
          </div> */}
          <WithdrawTip />
          {address ? (
            <div
              className={isDisabled ? `${styles.connectWallet} ${styles.disabled}` : styles.connectWallet}
              onClick={() => {
                if (isDisabled) {
                  return;
                }
                setTxId('');
                setModalVisible(true);
                window.gtag && window.gtag('event', 'withdraw_body_withdraw');
              }}
            >
              {intl.get('withdraw_withdraw_btn')}
            </div>
          ) : (
            <div
              className={styles.connectWallet}
              onClick={() => {
                showConnectModal();
                window.gtag && window.gtag('event', 'withdraw_body_connect');
              }}
            >
              {intl.get('connect_wallet')}
            </div>
          )}
        </div>
        {/* <GasFreeIntroduction /> */}
        {/* <div style={{ color: '#FFF', fontSize: 14, marginTop: 20 }}>
        我的账号地址: {address}
        <br />
        账户余额： {BigNumber(balance).toString()}
        <br />
        <br />
        我的GasFree地址: {userTron.gasFreeAddress}
        <br />
        GasFree账户余额： {BigNumber(gasFreeBalance).toString()}
        <br />
        <br />
        我的已经创建的GasFree地址: {userTron.deployedGasFrees}
        <br />
        <button
          onClick={() => {
            deployGasFreeAddress();
          }}
        >
          点击Deploy GasFree Address
        </button>
        <br />
        <br />
        <button
          onClick={() => {
            retrieveUserTRX();
          }}
        >
          retrieve TRX
        </button>
        <br />
        <br />
        <button
          onClick={() => {
            retrieveUser();
          }}
        >
          retrieve TRC20
        </button>
      </div> */}
        {/* {(env === 'test' || env === 'nile') && (
          <div>
            临时按钮
            <br />
            <div
              className={styles.connectWallet}
              style={{
                margin: '10px auto'
              }}
              onClick={() => {
                deployGasFreeAddress();
              }}
            >
              点击Deploy GasFree Address
            </div>
            <br />
            我的GasFree地址: {userTron.gasFreeAddress}
            <br />
            我的已经创建的GasFree地址: {userTron.deployedGasFrees}
            <br />
          </div>
        )} */}
        <WithdrawModal
          visible={modalVisible}
          receiveAddress={address}
          setOpen={value => setModalVisible(value)}
          confirm={confirm}
          txId={txId}
          token={token}
          modalLoading={modalLoading}
          setModalLoading={value => setModalLoading(value)}
        />
        <TokenModal
          visible={tokenModalVisible}
          selectedToken={token}
          tokenList={tokenList}
          fetchTokenList={fetchTokenList}
          onSelect={onSelectToken}
          onClose={() => setTokenModalVisible(false)}
        />
      </div>
    </PageContainer>
  );
});

export default WithdrawTron;
export function WithdrawTitle() {
  const [visible, setVisible] = useState(false);
  return (
    <div className={styles.withdrawTitle}>
      {intl.get('withdraw_title')}

      <div className={styles.transfer} onClick={() => setVisible(true)}>
        <span className="text">{intl.get('withdraw_go_transfer')}</span>
        <i className="icon"></i>
      </div>
      <TransferTipModal onClose={() => setVisible(false)} visible={visible} />
    </div>
  );
}

export function WithdrawTip() {
  return (
    <div className={styles.tipsWrap}>
      <div className="tip">
        <span className="decoration"></span>
        {intl.getHTML('withdraw_tip0')}
      </div>
      <div className="tip">
        <span className="decoration"></span>
        {intl.getHTML('withdraw_tip1')}
      </div>
    </div>
  );
}

export function GasFreeIntroduction() {
  const [visible, setVisible] = useState(true);
  function onClose() {
    setVisible(false);
  }
  return (
    <div className={classNames(styles.introWrap, visible ? '' : styles.hide)}>
      <span className="icon"></span>
      <CloseButton onClick={onClose} style={{ position: 'absolute', top: 30, right: 10, zIndex: 20 }} />
      <Carousel autoplay={true} autoplaySpeed={10000}>
        <div className="intro">
          <div className="title">{intl.getHTML('withdraw_gasfree_intro_title')}</div>
          <div className="text">{intl.get('withdraw_gasfree_intro_text')}</div>
        </div>
        <div className="intro">
          <div className="title">{intl.getHTML('withdraw_gasfree_intro_title2')}</div>
          <div className="text">{intl.get('withdraw_gasfree_intro_text2')}</div>
        </div>
      </Carousel>
    </div>
  );
}
