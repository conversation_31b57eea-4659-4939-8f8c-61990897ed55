import { observer } from 'mobx-react-lite';
import intl from 'react-intl-universal';
import { useEffect } from 'react';
import { Header } from '../NewHome/Header/Header';
import IntegrationsFooter from './components/IntegrationsFooter';
import PartnerCard from './components/PartnerCard';
import { partnerLinks } from './links';
import styles from './integrations.module.scss';

const Integrations = observer(() => {
  useEffect(() => {
    window.gtag && window.gtag('event', '3rdparty_load');
  }, []);
  return (
    <div className={styles.integrationsContainer}>
      <Header />

      <main className={styles.main}>
        {/* Hero Section */}
        <section className={styles.heroSection}>
          <div className={styles.heroContent}>
            <div className={styles.heroText}>
              <h1 className={styles.title}>{intl.get('integrations_title')}</h1>
              <p className={styles.subtitle}>{intl.get('integrations_subtitle')}</p>
              <p className={styles.description}>{intl.get('integrations_description')}</p>

              <div className={styles.ctaSection}>
                <h2 className={styles.ctaTitle}>
                  {intl.get('integrations_cta_title')}
                  <br />
                  <span className={styles.ctaHighlight}>{intl.get('integrations_cta_highlight')}</span>
                </h2>
                <a
                  href="https://developer.gasfree.io/register"
                  target="_blank"
                  rel="noopener noreferrer"
                  className={styles.ctaButton}
                  onClick={() => window.gtag && window.gtag('event', '3rdparty_body_join')}
                >
                  <span className={styles.buttonText}>{intl.get('integrations_cta_button')}</span>
                </a>
              </div>
            </div>
          </div>
        </section>

        {/* Partners Section */}
        <section className={styles.partnersSection}>
          <div className={styles.partnersContainer}>
            <div className={styles.partnersGrid}>
              {Object.entries(partnerLinks).map(([key, partner]) => (
                <PartnerCard key={key} partner={partner} />
              ))}
            </div>
          </div>

          <IntegrationsFooter />
        </section>
      </main>
    </div>
  );
});

export default Integrations;
