import React from 'react';
import styles from './integrationsFooter.module.scss';
import logoImage from '../../NewHome/images/logo.svg';
import twitterIcon from '../../NewHome/images/twitter-hover.svg';
import telegramIcon from '../../NewHome/images/telegram-hover.svg';
import discordIcon from '../../NewHome/images/discord-hover.svg';
import intl from 'react-intl-universal';
import { getLang, Lang } from '@/utils/select-lang';
import { ContractsSubMenu, DevelopersSubMenu, ProductsSubMenus, ResourcesSubMenu } from '@/pages/NewHome/utils/const';

const IntegrationsFooter: React.FC = () => {
  const menuItems = [
    {
      titleKey: 'newhome_footer_products',
      items: ProductsSubMenus
    },
    {
      titleKey: 'newhome_footer_developers',
      items: DevelopersSubMenu
    },
    {
      titleKey: 'newhome_footer_resources',
      items: ResourcesSubMenu
    },
    {
      titleKey: 'newhome_footer_contacts',
      items: ContractsSubMenu
    }
  ];
  const currentLang = getLang();

  // 根据当前语言获取对应的链接
  const getLocalizedUrl = (baseUrl: string, lang: Lang) => {
    // 针对帮助中心链接的语言区分
    if (baseUrl.includes('support.tronlink.org')) {
      switch (lang) {
        case 'zh-CN':
          return 'https://support.tronlink.org/hc/zh-cn/articles/38903684778393-GasFree-%E9%92%B1%E5%8C%85%E4%BD%BF%E7%94%A8%E6%96%87%E6%A1%A3';
        case 'zh-TC':
          return 'https://support.tronlink.org/hc/zh-cn/articles/38903684778393-GasFree-%E9%92%B1%E5%8C%85%E4%BD%BF%E7%94%A8%E6%96%87%E6%A1%A3';
        case 'en-US':
        default:
          return 'https://support.tronlink.org/hc/en-us/articles/38903684778393-GasFree-User-Guide';
      }
    }
    // 其他链接保持原样
    return baseUrl;
  };

  const getFooterEvent = (event: string) => {
    const pathname = window.location.pathname;
    if (pathname.startsWith('/faq')) return `faq_footer_${event}`;
    if (pathname.startsWith('/withdraw')) return `withdraw_footer_${event}`;
    if (pathname.startsWith('/3rdparty')) return `3rdparty_footer_${event}`;
    if (pathname.startsWith('/privacy')) return `privacy_footer_${event}`;
    if (pathname.startsWith('/terms')) return `terms_footer_${event}`;
    return `home_footer_${event}`;
  };

  return (
    <footer className={styles.footer}>
      <div className={styles.container}>
        <div className={styles.logoColumn}>
          <img src={logoImage} alt="GasFree Logo" className={styles.logo} />
          <div className={styles.socialIcons}>
            <a
              href="https://discord.gg/2KdByBgBA3"
              target="_blank"
              rel="noopener noreferrer"
              className={styles.socialIcon}
              onClick={() => window.gtag && window.gtag('event', getFooterEvent('discord'))}
            >
              <img src={discordIcon} alt="GitHub" />
            </a>
            <a
              href="https://twitter.com/DeFi_JUST"
              target="_blank"
              rel="noopener noreferrer"
              className={styles.socialIcon}
              onClick={() => window.gtag && window.gtag('event', getFooterEvent('twitter'))}
            >
              <img src={twitterIcon} alt="Twitter" />
            </a>
            <a
              href="https://t.me/DeFi_JUST"
              target="_blank"
              rel="noopener noreferrer"
              className={styles.socialIcon}
              onClick={() => window.gtag && window.gtag('event', getFooterEvent('telegram'))}
            >
              <img src={telegramIcon} alt="Telegram" />
            </a>
          </div>
        </div>

        {menuItems.map(section => (
          <div key={section.titleKey} className={styles.menuColumn}>
            <h3 className={styles.menuTitle}>{intl.get(section.titleKey)}</h3>
            <ul className={styles.menuList}>
              {section.items.map((item, index) => (
                <li key={index} className={styles.menuItem}>
                  <a
                    href={getLocalizedUrl(item.href, currentLang)}
                    target={item.target || '_self'}
                    className={`${styles.menuLink} ${item.label === intl.get('newhome_nav_join') ? styles.joinLink : ''}`}
                    onClick={() =>
                      item.event &&
                      window.gtag &&
                      window.gtag('event', getFooterEvent(item.event.replace('home_header_', '')))
                    }
                  >
                    {item.label}
                  </a>
                </li>
              ))}
            </ul>
          </div>
        ))}
      </div>

      <div className={styles.bottomBar}>
        <div className={styles.copyright}>
          Copyright© 2024-2025
          <strong className={styles.version}>V1.1.0</strong>
        </div>
        <div className={styles.links}>
          <a
            href="/privacy"
            className={styles.link}
            onClick={() => window.gtag && window.gtag('event', getFooterEvent('privacy'))}
          >
            {intl.get('newhome_footer_privacy')}
          </a>
          <a
            href="/terms"
            className={styles.link}
            onClick={() => window.gtag && window.gtag('event', getFooterEvent('terms'))}
          >
            {intl.get('newhome_footer_terms')}
          </a>
        </div>
      </div>
    </footer>
  );
};

export default IntegrationsFooter;
