.footer {
  width: 100vw;
  margin-left: calc(-50vw + 50%);
  padding: 0;
  background: transparent;
  position: relative;
  z-index: 2;
  height: auto;
  margin-top: 0;
  padding-bottom: 0;
  padding-top: 170px;

  .container {
    max-width: 1242px;
    margin: 20px auto 0;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 40px;
    padding: 60px 60px 30px;
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 40px;
    margin-bottom: 0;
    text-align: left;
    min-height: 320px;
    backdrop-filter: blur(20px);
  }

  .logoColumn {
    .logo {
      height: 40px;
      margin-bottom: 24px;
      display: block;
    }

    .socialIcons {
      display: flex;
      gap: 30px;
      margin-bottom: 24px;

      .socialIcon {
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: rgba(255, 255, 255, 0.5);
        transition: all 0.3s ease;
        border-radius: 8px;

        img {
          width: 20px;
          height: 20px;
          transition: opacity 0.3s ease;
          opacity: 0.6;
        }

        &:hover img {
          opacity: 1;
        }
      }
    }
  }

  .menuColumn {
    .menuTitle {
      font-family: 'Sora', sans-serif;
      font-size: 16px;
      font-weight: 600;
      color: #ffffff;
      margin: 0 0 40px;
      text-align: left;
    }

    .menuList {
      list-style: none;
      padding: 0;
      margin: 0;
      text-align: left;

      .menuItem {
        margin-bottom: 25px;
        line-height: 18px;

        &:last-child {
          margin-bottom: 0;
        }

        .menuLink {
          font-family: 'Sora', sans-serif;
          font-size: 14px;
          color: rgba(255, 255, 255, 0.7);
          text-decoration: none;
          transition: color 0.3s ease;
          display: inline-block;

          &:hover {
            color: #33ffa9;
          }
        }

        .joinLink {
          display: inline-flex;
          align-items: center;
          gap: 8px;
          font-weight: 500;
          transition: all 0.3s ease;

          &::before {
            content: '+';
            width: 16px;
            height: 16px;
            background-color: rgba(51, 255, 169, 0.7);
            border-radius: 2px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            font-weight: bold;
            color: #fff;
            flex-shrink: 0;
          }

          &:hover {
            color: #33ffa9 !important;

            &::before {
              background-color: #2ee89a;
            }
          }
        }
      }
    }
  }

  .bottomBar {
    max-width: 1242px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 20px 30px;
    height: auto;
    min-height: 50px;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 20px;
      right: 20px;
      height: 1px;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    }

    .copyright {
      font-family: 'Sora', 'Wix Madefor Display', sans-serif;
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      letter-spacing: 0%;
      color: rgba(255, 255, 255, 0.5);
      text-transform: capitalize;
      text-align: left;
    }

    .version {
      font-size: 14px;
      color: #fff;
      font-weight: 400;
      margin-left: 20px;
    }

    .links {
      display: flex;
      gap: 24px;

      .link {
        font-family: 'Sora', sans-serif;
        font-size: 14px;
        line-height: 20px;
        color: rgba(255, 255, 255, 0.5);
        text-decoration: none;
        transition: color 0.3s ease;

        &:hover {
          color: #33ffa9;
        }
      }
    }
  }
}

/* 移动端响应式设计 */
@media (max-width: 1024px) {
  .footer {
    .container {
      max-width: 90%;
      padding: 50px 40px 25px;
      min-height: 280px;
    }

    .bottomBar {
      max-width: 90%;
      padding: 20px 40px 30px;
    }
  }
}

@media (max-width: 768px) {
  .footer {
    padding-top: 60px;

    .container {
      max-width: 90%;
      margin: 10px auto 0;
      padding: 30px 24px 20px;
      border-radius: 24px;
      grid-template-columns: repeat(2, 1fr);
      gap: 24px 20px;
      min-height: auto;
    }

    .logoColumn {
      grid-column: span 2;
      text-align: left;
      margin-bottom: 12px;

      .logo {
        height: 32px;
        margin-bottom: 20px;
      }

      .socialIcons {
        justify-content: flex-start;
        gap: 16px;
        margin-bottom: 0;
      }
    }

    .menuColumn {
      text-align: left;

      .menuTitle {
        font-size: 14px;
        margin-bottom: 20px;
        font-weight: 600;
      }

      .menuList {
        .menuItem {
          margin-bottom: 12px;

          .menuLink {
            font-size: 13px;
          }
        }
      }
    }

    .bottomBar {
      max-width: 90%;
      margin: 0 auto;
      flex-direction: column;
      gap: 12px;
      padding: 16px 24px 25px;

      &::before {
        left: 24px;
        right: 24px;
      }

      .copyright {
        font-size: 12px;
        order: 1;
        text-align: center;
      }

      .version {
        font-size: 12px;
      }

      .links {
        gap: 24px;
        order: 0;
        justify-content: center;

        .link {
          font-size: 12px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .footer {
    padding-top: 40px;

    .container {
      max-width: 95%;
      padding: 24px 20px 18px;
      border-radius: 20px;
      gap: 20px 16px;
    }

    .logoColumn {
      margin-bottom: 10px;

      .logo {
        height: 28px;
        margin-bottom: 16px;
      }

      .socialIcons {
        gap: 16px;

        .socialIcon {
          width: 18px;
          height: 18px;

          img {
            width: 18px;
            height: 18px;
          }
        }
      }
    }

    .menuColumn {
      .menuTitle {
        font-size: 13px;
        margin-bottom: 16px;
      }

      .menuList {
        .menuItem {
          margin-bottom: 10px;

          .menuLink {
            font-size: 12px;
          }

          .joinLink {
            gap: 6px;

            &::before {
              width: 14px;
              height: 14px;
              font-size: 16px;
            }
          }
        }
      }
    }

    .bottomBar {
      max-width: 95%;
      padding: 14px 20px 20px;
      gap: 10px;

      &::before {
        left: 20px;
        right: 20px;
      }

      .copyright {
        font-size: 11px;
      }

      .links {
        gap: 20px;

        .link {
          font-size: 11px;
        }
      }
    }
  }
}
