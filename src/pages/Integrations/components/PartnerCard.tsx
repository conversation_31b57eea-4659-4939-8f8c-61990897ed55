import React from 'react';
import { PartnerInfo } from '../links';
import styles from '../integrations.module.scss';

interface PartnerCardProps {
  partner: PartnerInfo;
}

const PartnerCard: React.FC<PartnerCardProps> = ({ partner }) => {
  const handleLinkClick = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  const handleWebsiteClick = () => {
    if (partner.links.website) {
      window.gtag && window.gtag('event', `3rdparty_body_${partner.icon}_website`);
      handleLinkClick(partner.links.website);
    }
  };

  const handleAppStoreClick = () => {
    window.gtag && window.gtag('event', `3rdparty_body_${partner.icon}_appstore`);
    handleLinkClick(partner.links.appstore!);
  };
  const handlePlayStoreClick = () => {
    window.gtag && window.gtag('event', `3rdparty_body_${partner.icon}_android`);
    handleLinkClick(partner.links.playstore!);
  };
  const handleChromeClick = () => {
    window.gtag && window.gtag('event', `3rdparty_body_${partner.icon}_chrome`);
    handleLinkClick(partner.links.chrome!);
  };
  const handleDesktopClick = () => {
    window.gtag && window.gtag('event', `3rdparty_body_${partner.icon}_desktop`);
    handleLinkClick(partner.links.desktop!);
  };

  return (
    <div className={`${styles.partnerCard} ${styles[`${partner.icon}Card`]}`}>
      <div className={styles.cardHeader}>
        <div
          className={styles.websiteClickArea}
          onClick={handleWebsiteClick}
          style={{ cursor: partner.links.website ? 'pointer' : 'default' }}
        >
          <div className={styles.partnerIcon}>
            <div className={styles[`${partner.icon}Icon`]}></div>
          </div>
          <h3 className={styles.partnerName}>{partner.name}</h3>
        </div>
        <div className={styles.cardActions}>
          {partner.links.appstore && (
            <button
              className={`${styles.actionBtn} ${styles.appstore}`}
              onClick={handleAppStoreClick}
              title="Download on App Store"
            >
              <span>App Store</span>
            </button>
          )}
          {partner.links.playstore && (
            <button
              className={`${styles.actionBtn} ${styles.playstore}`}
              onClick={handlePlayStoreClick}
              title="Get it on Google Play"
            >
              <span>Play Store</span>
            </button>
          )}
          {partner.links.chrome && (
            <button
              className={`${styles.actionBtn} ${styles.chrome}`}
              onClick={handleChromeClick}
              title="Install Chrome Extension"
            >
              <span>Chrome Extension</span>
            </button>
          )}
          {partner.links.desktop && (
            <button
              className={`${styles.actionBtn} ${styles.desktop}`}
              onClick={handleDesktopClick}
              title="Download Desktop App"
            >
              <span>Desktop App</span>
            </button>
          )}
        </div>
      </div>
      <p className={styles.partnerDescription}>{partner.description}</p>
    </div>
  );
};

export default PartnerCard;
