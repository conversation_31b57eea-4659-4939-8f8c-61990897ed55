import intl from 'react-intl-universal';

export interface PartnerLinks {
  appstore?: string;
  playstore?: string;
  chrome?: string;
  desktop?: string;
  website?: string;
}

export interface PartnerInfo {
  name: string;
  description: string;
  icon: string;
  links: PartnerLinks;
}

// 集中管理所有合作伙伴的链接
export const partnerLinks: Record<string, PartnerInfo> = {
  tronlink: {
    name: 'TronLink',
    description: intl.get('integrations_tronlink_desc'),
    icon: 'tronlink',
    links: {
      appstore: 'https://apps.apple.com/us/app/tronlink-trx-btt-wallet/id1453530188',
      playstore: 'https://play.google.com/store/apps/details?id=com.tronlink.global',
      chrome: 'https://chromewebstore.google.com/detail/tronlink/ibnejdfjmmkpcnlpebklmnkoeoihofec',
      website: 'https://www.tronlink.org/'
    }
  },
  imtoken: {
    name: 'imToken',
    description: intl.get('integrations_imtoken_desc'),
    icon: 'imtoken',
    links: {
      appstore: 'https://apps.apple.com/us/app/imtoken-btc-eth-wallet/id1384798940',
      playstore: 'https://play.google.com/store/apps/details?id=im.token.app',
      website: 'https://token.im/'
    }
  },
  guarda: {
    name: 'Guarda',
    description: intl.get('integrations_guarda_desc'),
    icon: 'guarda',
    links: {
      appstore: 'https://apps.apple.com/us/app/guarda-crypto-wallet-bitcoin/id1442083982',
      playstore: 'https://play.google.com/store/apps/details?id=com.crypto.multiwallet',
      desktop: 'https://guarda.com/download/',
      website: 'https://guarda.com/'
    }
  },
  klever: {
    name: 'Klever Wallet',
    description: intl.get('integrations_klever_desc'),
    icon: 'klever',
    links: {
      appstore: 'https://apps.apple.com/sr/app/klever-wallet-crypto-btc-eth/id1615064243',
      playstore: 'https://play.google.com/store/apps/details?id=finance.klever.bitcoin.wallet',
      chrome: 'https://chromewebstore.google.com/detail/klever-wallet/ifclboecfhkjbpmhgehodcjpciihhmif',
      website: 'https://klever.io/'
    }
  },
  edir: {
    name: 'eDir',
    description: intl.get('integrations_edir_desc'),
    icon: 'edir',
    links: {
      playstore: 'https://play.google.com/store/apps/details?id=so.bixi.edir',
      website: 'https://edir.so/'
    }
  }
};
