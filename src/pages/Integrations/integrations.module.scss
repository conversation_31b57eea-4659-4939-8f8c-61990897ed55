.integrationsContainer {
  min-height: 100vh;
  height: 100vh;
  background: #000;
  color: white;
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
}

.main {
  padding-top: 40px;
}

.heroSection {
  display: flex;
  // align-items: center;
  position: relative;
  background: url('./images/bg.png');
  background-size: 1440px 830px;
  background-position: center -140px;
  background-repeat: no-repeat;
  overflow: hidden;
  padding: 40px 0;
  height: 540px;
}

.heroContent {
  max-width: 1140px;
  width: 100%;
  margin: 0 auto;
  padding: 0;
  // min-height: calc(100vh - 80px);
}

.heroText {
  z-index: 2;
  max-width: 800px;
}

.title {
  font-size: 50px;
  font-weight: 800;
  line-height: 1;
  margin: 0 0 24px 0;
  color: white;
  text-align: left;
}

.subtitle {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.5);
  text-align: left;
  max-width: 576px;
  line-height: 21px;
}

.description {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.5);
  text-align: left;
  margin-bottom: 60px;
  line-height: 21px;
}

.ctaSection {
  border-radius: 20px;
  background: rgba(195, 190, 221, 0.15);
  backdrop-filter: blur(20px);
  padding: 24px;
  margin-top: 32px;
  width: 400px;
  text-align: left;
}

.ctaTitle {
  font-size: 20px;
  font-weight: 700;
  margin: 0 0 24px 0;
  line-height: 25px;
  color: white;
  text-align: left;
}

.ctaHighlight {
  background: linear-gradient(88.14deg, #032c90 -3.94%, #21cffb 7.17%, #33ffa9 40.49%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-align: left;
  font-size: 20px;
  line-height: 25px;
}

.ctaButton {
  background: rgba(51, 255, 169, 1);
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  font-size: 16px;
  font-weight: 700;
  line-height: 20px;
  color: #000;
  cursor: pointer;
  height: 40px;
  display: inline-block;
  position: relative;
  overflow: hidden;
  white-space: nowrap;
  text-align: center;

  // Calypso 按钮效果实现
  .buttonText {
    display: block;
    position: relative;
    z-index: 10;
    transition: color 0.4s ease;
    opacity: 1;
    color: #000;
    font-weight: 700;
  }

  &::before {
    content: '';
    position: absolute;
    background: #fff;
    width: 120%;
    height: 0;
    padding-bottom: 120%;
    top: -110%;
    left: -10%;
    border-radius: 50%;
    transform: translate3d(0, 68%, 0) scale3d(0, 0, 0);
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #fff;
    transform: translate3d(0, -100%, 0);
    transition: transform 0.4s cubic-bezier(0.1, 0, 0.3, 1);
  }

  &:focus {
    outline: none;
  }

  &:hover {
    &::before {
      transform: translate3d(0, 0, 0) scale3d(1, 1, 1);
      transition: transform 0.4s cubic-bezier(0.1, 0, 0.3, 1);
    }

    &::after {
      transform: translate3d(0, 0, 0);
      transition-duration: 0.05s;
      transition-delay: 0.4s;
      transition-timing-function: linear;
    }

    .buttonText {
      color: #000;
      animation:
        moveScaleUpInitial 0.3s forwards,
        moveScaleUpEnd 0.3s forwards 0.3s;
    }
  }
}
.partnersContainer {
  max-width: 1180px;
  margin: 0 auto;
  padding: 0 20px;
}

.cardHeader {
  display: flex;
  gap: 12px;
  padding: 20px;
  position: relative;
}

.partnerIcon {
  flex-shrink: 0;

  div {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
  }

  img {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    object-fit: contain;
  }

  svg {
    border-radius: 8px;
  }
}

.tronlinkIcon {
  background-image: url('./images/TronLink.png');
}

.imtokenIcon {
  background-image: url('./images/imToken.png');
}

.guardaIcon {
  background-image: url('./images/Guarda.png');
}

.kleverIcon {
  background-image: url('./images/Klever.png');
}

.edirIcon {
  background-image: url('./images/eDir.png');
}

.partnerName {
  color: white;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  line-height: 1.2;
}

.cardActions {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  background: rgba(0, 0, 0, 0.25);
  border-radius: 5px;
  padding: 5px 15px;
  height: 30px;
}

.actionBtn {
  width: 20px;
  height: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 10px;
  border: none;
  transition: all 0.2s ease;
  padding: 0;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  border-radius: 5px;
  // background-color: rgba(0, 0, 0, 0.2);
  background-color: transparent;

  // 移除点击后的样式
  &:focus {
    outline: none;
  }

  &:active {
    transform: none;
    box-shadow: none;
  }

  span {
    display: none; // 隐藏文字，只显示图标
  }

  img {
    width: 20px;
    height: 20px;
    transition: opacity 0.2s ease;
  }
}

// 操作按钮图标样式
.appstore {
  background-image: url('./images/appstore.svg');

  &:hover {
    background-image: url('./images/appstoreHover.svg');
  }
}

.playstore {
  background-image: url('./images/play.svg');

  &:hover {
    background-image: url('./images/playHover.svg');
  }
}

.chrome {
  background-image: url('./images/chrome.svg');

  &:hover {
    background-image: url('./images/chromeHover.svg');
  }
}

.desktop {
  background-image: url('./images/desktop.svg');

  &:hover {
    background-image: url('./images/desktopHover.svg');
  }
}

.partnerDescription {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  line-height: 1.4;
  text-align: left;
  margin: 0;
  padding: 0 20px 20px 20px;
}

.partnersSection {
  margin-top: 40px;
  position: relative;
  background: url('./images/footerBg.svg');
  background-size: auto;
  background-position: center bottom;
  background-repeat: no-repeat;
  min-height: 980px;
}

.partnersGrid {
  max-width: 1140px;
  width: 1140px;
  margin: 0 auto;
  padding: 0;
  display: grid;
  grid-template-columns: repeat(2, 560px);
  gap: 20px;
  justify-content: center;
}

.partnerCard {
  // border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  background: rgba(195, 190, 221, 0.15);
  backdrop-filter: blur(20px);
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  width: 560px;
  height: 140px;
  display: flex;
  flex-direction: column;

  &:hover {
    transform: translateY(-2px);
  }

  &.extended {
    grid-column: span 1;

    .partnerInfo {
      background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 100%);
    }

    .partnerName {
      color: white;
    }

    .arrow {
      opacity: 1;
    }
  }
}

// 合作伙伴卡片的hover背景
.tronlinkCard:hover {
  background-image: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3)), url('./images/TronLinkHoverBg.png');
}

.imtokenCard:hover {
  background-image: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3)), url('./images/imTokenHoverBg.png');
}

.guardaCard:hover {
  background-image: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3)), url('./images/GuardaHoverBg.png');
}

.kleverCard:hover {
  background-image: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3)), url('./images/KleverHoverBg.png');
}

.edirCard:hover {
  background-image: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3)), url('./images/edirHoverBg.png');
}

.websiteClickArea {
  position: relative;
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;

  .partnerName {
    position: relative;
    display: inline-flex;
    align-items: center;

    &::after {
      content: '';
      width: 16px;
      height: 16px;
      background-image: url('./images/arrow.svg');
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
      margin-left: 8px;
      opacity: 0;
      transform: translateX(-10px);
      transition: all 0.3s ease;
    }
  }

  &:hover .partnerName::after {
    opacity: 1;
    transform: translateX(0);
  }
}

.cardIcons {
  display: flex;
  gap: 8px;
  align-items: center;
}

// Calypso 按钮文字动画
@keyframes moveScaleUpInitial {
  to {
    transform: translate3d(0, -100%, 0) scale3d(1, 1.5, 1);
    opacity: 0;
  }
}

@keyframes moveScaleUpEnd {
  from {
    transform: translate3d(0, 100%, 0) scale3d(1, 1.5, 1);
    opacity: 0;
  }
  to {
    transform: translate3d(0, 0, 0) scale3d(1, 1, 1);
    opacity: 1;
  }
}

// 移动端响应式设计
@media (max-width: 1200px) {
  .heroContent {
    max-width: 95%;
    width: 95%;
    padding: 0 10px;
  }

  .partnersGrid {
    max-width: 95%;
    width: 95%;
    padding: 0 10px;
  }
}

@media (max-width: 1024px) {
  .heroSection {
    background-size: 1200px auto;
    background-position: center -100px;
    height: auto;
    padding: 30px 0;
  }

  .heroContent {
    max-width: 90%;
    width: 90%;
    padding: 0 20px;
  }

  .title {
    font-size: 40px;
  }

  .ctaSection {
    width: 350px;
    margin-top: 160px;
  }

  .partnersGrid {
    max-width: 90%;
    width: 90%;
    padding: 0 20px;
  }
}

@media (max-width: 768px) {
  .main {
    padding-top: 20px;
  }

  .heroSection {
    background: url('./images/mobileBg.png');
    background-size: cover;
    background-position: center -60px;
    background-repeat: no-repeat;
    padding: 20px 0;
  }

  .heroContent {
    max-width: 100%;
    width: 100%;
    padding: 0 16px;
  }

  .heroText {
    max-width: 100%;
  }

  .title {
    font-size: 32px;
    margin-bottom: 16px;
    line-height: 1.2;
  }

  .subtitle,
  .description {
    font-size: 13px;
    line-height: 1.4;
  }

  .ctaSection {
    width: 100%;
    max-width: 320px;
    padding: 20px;
    margin-top: 24px;
  }

  .ctaTitle {
    font-size: 18px;
    line-height: 22px;
    margin-bottom: 20px;
  }

  .ctaHighlight {
    font-size: 18px;
    line-height: 22px;
  }

  .ctaButton {
    width: 100%;
    padding: 12px 20px;
    height: 44px;
    font-size: 16px;
  }

  .partnersSection {
    margin-top: -20px;
    min-height: auto;
    padding-bottom: 40px;
  }

  .partnersContainer {
    padding: 0 16px;
    margin-top: 20px;
  }

  .partnersGrid {
    max-width: 100%;
    width: 100%;
    margin: 0 auto;
    padding: 0;
    display: flex;
    flex-direction: column;
    gap: 16px;
    grid-template-columns: none;
  }

  .partnerCard {
    width: 100%;
    height: auto;
    min-height: 120px;
    flex-direction: column;
  }

  .cardHeader {
    padding: 16px;
    gap: 10px;
  }

  .partnerIcon {
    div {
      width: 36px;
      height: 36px;
    }

    img {
      width: 36px;
      height: 36px;
    }
  }

  .partnerName {
    font-size: 16px;
  }

  .cardActions {
    position: static;
    background: none;
    padding: 0;
    height: auto;
    margin-left: auto;
  }

  .actionBtn {
    width: 18px;
    height: 18px;
    margin: 0 5px;
  }

  .partnerDescription {
    padding: 0 16px 16px 16px;
    font-size: 11px;
    line-height: 1.3;
  }

  .websiteClickArea {
    gap: 10px;

    .partnerName::after {
      width: 14px;
      height: 14px;
      margin-left: 6px;
    }
  }
}

@media (max-width: 480px) {
  .heroSection {
    background: url('./images/mobileBg.png');
    background-size: contain;
    background-position: top 20px right;
    background-repeat: no-repeat;
    padding: 16px 0;
  }

  .partnersSection {
    padding-bottom: 30px;
  }

  .title {
    font-size: 28px;
  }

  .subtitle,
  .description {
    font-size: 12px;
  }

  .ctaSection {
    max-width: 280px;
    padding: 16px;
  }

  .ctaTitle {
    font-size: 16px;
    line-height: 20px;
    margin-bottom: 16px;
  }

  .ctaHighlight {
    font-size: 16px;
    line-height: 20px;
  }

  .ctaButton {
    height: 40px;
    font-size: 14px;
  }

  .cardHeader {
    padding: 14px;
  }

  .partnerIcon {
    div {
      width: 32px;
      height: 32px;
    }

    img {
      width: 32px;
      height: 32px;
    }
  }

  .partnerName {
    font-size: 15px;
  }

  .actionBtn {
    width: 16px;
    height: 16px;
    margin: 0 4px;
  }

  .partnerDescription {
    padding: 0 14px 14px 14px;
    font-size: 10px;
  }
}
