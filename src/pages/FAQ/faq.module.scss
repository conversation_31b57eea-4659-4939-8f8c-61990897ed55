.faqContainer {
  min-height: 100vh;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #000;
  color: #ffffff;
  overflow-x: hidden;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.main {
  margin-top: 100px;
  padding-bottom: 0;
  z-index: 1;
  position: relative;
  background: url('../Integrations/images/footerBg.svg');
  background-size: auto;
  background-position: center bottom;
  background-repeat: no-repeat;
}

.title {
  font-size: 48px;
  font-weight: 700;
  text-align: center;
  margin-bottom: 60px;
}

.faqList {
  display: flex;
  flex-direction: column;
  // gap: 16px;
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 20px 40px 80px;
  width: 100%;
  box-sizing: border-box;
  min-height: 540px;
}

.faqItem {
  // background: rgba(255, 255, 255, 0.05);
  border-radius: 0;
  padding: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.15);

  // &:hover {
  //   background: rgba(255, 255, 255, 0.08);
  // }

  // &.open {
  //   background: rgba(255, 255, 255, 0.1);
  // }
}

.questionRow {
  display: flex;
  align-items: center;
  gap: 16px;
  justify-content: space-between;
  width: 100%;
}

.icon {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  object-fit: contain;
}

.question {
  flex: 1;
  font-size: 16px;
  font-weight: 700;
  color: rgba(255, 255, 255, 1);
  text-align: left;
  min-width: 0;
  word-wrap: break-word;
  overflow-wrap: break-word;
  max-width: 600px;
  margin: 0 auto;
}

.toggleIcon {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  object-fit: contain;
  transition: transform 0.3s ease;

  .open & {
    transform: rotate(180deg);
  }
}

.answer {
  font-size: 14px;
  line-height: 26px;
  color: #fff9;
  width: 100%;
  max-width: 600px;
  margin: 16px auto 0;
  text-align: center;
  word-wrap: break-word;
  overflow-wrap: break-word;
  box-sizing: border-box;
  text-align: left;
}

/* 移动端响应式设计 */
@media (max-width: 1024px) {
  .faqList {
    max-width: 95%;
    padding: 0 24px;
  }

  .footerSection {
    background-size: 1200px auto;
    background-position: center bottom;
    min-height: 350px;
    padding-top: 60px;
  }
}

@media (max-width: 768px) {
  .faqContainer {
    font-size: 16px;
    background: url('../Integrations/images/m-footerBg.svg');
    background-size: cover;
    background-position: center bottom;
    background-repeat: no-repeat;
  }

  .main {
    margin-top: 0;
    padding-top: 80px;
    padding-bottom: 15px;
    min-height: calc(100vh - 80px);
    background: none;
  }

  .footerSection {
    background-size: 1000px auto;
    background-position: center bottom;
    min-height: 320px;
    margin-top: -15px;
    padding-top: 50px;
  }

  .title {
    font-size: 36px;
    margin-bottom: 40px;
    line-height: 1.2;
  }

  .faqList {
    padding: 0 20px;
    gap: 12px;
  }

  .faqItem {
    padding: 20px;
  }

  .questionRow {
    gap: 12px;
  }

  .question {
    font-size: 15px;
    line-height: 1.3;
  }

  .answer {
    font-size: 14px;
    line-height: 24px;
    margin: 12px 0 0 36px;
    padding-right: 36px;
  }

  .icon {
    width: 20px;
    height: 20px;
  }

  .toggleIcon {
    width: 20px;
    height: 20px;
  }
}

@media (max-width: 480px) {
  .main {
    margin-top: 0;
    padding-top: 70px;
    padding-bottom: 10px;
    min-height: calc(100vh - 70px);
  }

  .footerSection {
    background-size: 800px auto;
    min-height: 280px;
    margin-top: -10px;
    padding-top: 40px;
  }

  .title {
    font-size: 28px;
    margin-bottom: 30px;
    line-height: 1.2;
  }

  .faqList {
    padding: 0 16px;
    gap: 10px;
  }

  .faqItem {
    padding: 16px;
  }

  .questionRow {
    gap: 10px;
  }

  .question {
    font-size: 14px;
    line-height: 1.2;
  }

  .answer {
    font-size: 13px;
    line-height: 22px;
    margin: 10px 0 0 30px;
    padding-right: 30px;
  }

  .icon {
    width: 18px;
    height: 18px;
  }

  .toggleIcon {
    width: 18px;
    height: 18px;
  }
}

@media (max-width: 375px) {
  .main {
    margin-top: 0;
    padding-top: 60px;
    padding-bottom: 8px;
  }

  .footerSection {
    background-size: 600px auto;
    min-height: 250px;
    margin-top: -8px;
    padding-top: 30px;
  }

  .title {
    font-size: 24px;
    margin-bottom: 24px;
  }

  .faqList {
    padding: 0 12px;
  }

  .faqItem {
    padding: 14px;
  }

  .questionRow {
    gap: 8px;
  }

  .question {
    font-size: 13px;
    word-break: break-word;
  }

  .answer {
    font-size: 12px;
    line-height: 20px;
    margin: 8px 0 0 26px;
    padding-right: 26px;
    word-break: break-word;
  }

  .icon {
    width: 16px;
    height: 16px;
  }

  .toggleIcon {
    width: 16px;
    height: 16px;
  }
}

/* 横屏模式优化 */
@media (max-width: 768px) and (orientation: landscape) {
  .main {
    padding-top: 60px;
    padding-bottom: 10px;
    margin-top: 0;
  }

  .footerSection {
    min-height: 240px;
    margin-top: -10px;
    padding-top: 30px;
  }

  .title {
    font-size: 32px;
    margin-bottom: 30px;
  }

  .faqList {
    gap: 10px;
  }

  .faqItem {
    padding: 16px;
  }
}
