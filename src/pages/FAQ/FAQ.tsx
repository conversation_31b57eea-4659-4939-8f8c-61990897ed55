import { useState, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { Header } from '../NewHome/Header/Header';
import IntegrationsFooter from '../Integrations/components/IntegrationsFooter';
import styles from './faq.module.scss';
import icon1 from './images/icon1.svg';
import icon2 from './images/icon2.svg';
import icon3 from './images/icon3.svg';
import icon4 from './images/icon4.svg';
import expandIcon from './images/expand.svg';
import foldIcon from './images/fold.svg';
import intl from 'react-intl-universal';

interface FAQItemProps {
  icon: string;
  question: string;
  answer: string;
  isOpen: boolean;
  onClick: () => void;
}

const FAQItem = ({ icon, question, answer, isOpen, onClick }: FAQItemProps) => {
  return (
    <div
      className={`${styles.faqItem} ${isOpen ? styles.open : ''}`}
      onClick={() => {
        onClick();
        window.gtag && window.gtag('event', isOpen ? undefined : `faq_body_expand${question.slice(-1)}`);
        window.gtag && window.gtag('event', isOpen ? `faq_body_collapse${question.slice(-1)}` : undefined);
      }}
    >
      <div className={styles.questionRow}>
        <img src={icon} alt="" className={styles.icon} />
        <div className={styles.question}>{question}</div>
        <img src={isOpen ? foldIcon : expandIcon} alt={isOpen ? 'fold' : 'expand'} className={styles.toggleIcon} />
      </div>
      {isOpen && <div className={styles.answer} dangerouslySetInnerHTML={{ __html: answer }}></div>}
    </div>
  );
};

const FAQ = observer(() => {
  useEffect(() => {
    window.gtag && window.gtag('event', 'faq_load');
  }, []);
  const [openIndex, setOpenIndex] = useState<number>(0);

  const faqData = [
    {
      icon: icon1,
      question: intl.get('newhome_faqs_q1'),
      answer: intl.get('newhome_faqs_a1')
    },
    {
      icon: icon2,
      question: intl.get('newhome_faqs_q2'),
      answer: intl.get('newhome_faqs_a2')
    },
    {
      icon: icon3,
      question: intl.get('newhome_faqs_q3'),
      answer: intl.get('newhome_faqs_a3')
    },
    {
      icon: icon4,
      question: intl.get('newhome_faqs_q4'),
      answer: intl.get('newhome_faqs_a4')
    }
  ];

  const handleItemClick = (index: number) => {
    setOpenIndex(index === openIndex ? -1 : index);
  };

  return (
    <div className={styles.faqContainer}>
      <Header />

      <main className={styles.main}>
        <h1 className={styles.title}>FAQ</h1>

        <div className={styles.faqList}>
          {faqData.map((item, index) => (
            <FAQItem
              key={index}
              icon={item.icon}
              question={item.question}
              answer={item.answer}
              isOpen={index === openIndex}
              onClick={() => handleItemClick(index)}
            />
          ))}
        </div>
        <IntegrationsFooter />
      </main>
    </div>
  );
});

export default FAQ;
