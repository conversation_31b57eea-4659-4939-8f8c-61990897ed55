import { observer } from 'mobx-react-lite';
import { useEffect } from 'react';
import { Header } from './Header/Header';
import { HeroSection } from './HeroSection/HeroSection';
import FeaturesSection from './FeaturesSection/FeaturesSection';
import EcosystemSection from './EcosystemSection/EcosystemSection';
import CommunitySection from './CommunitySection/CommunitySection';
import CommunityBottomSection from './CommunitySection/CommunityBottomSection';
import styles from './newHome.module.scss';

declare global {
  interface Window {
    gtag?: (...args: any[]) => void;
  }
}

const NewHome = observer(() => {
  useEffect(() => {
    window.gtag && window.gtag('event', 'home_load');
  }, []);
  return (
    <div className={styles.newHomeContainer}>
      <Header />
      <HeroSection />
      <FeaturesSection />
      <EcosystemSection />
      <CommunitySection />
      <CommunityBottomSection />
    </div>
  );
});

export default NewHome;
