import React, { useRef, useState, useLayoutEffect, useEffect, useCallback } from 'react';
import styles from './MobileMenu.module.scss';
import classnames from 'classnames';
import {
  CommunitySubMenu,
  ContractsSubMenu,
  DevelopersSubMenu,
  LinkConfig,
  ProductsSubMenus,
  ResourcesSubMenu
} from '../utils/const';
import intl from 'react-intl-universal';
import { SwitchChain } from '@/components/SwitchChain/SwitchChain';
import commonStyles from '@/components/Common/common.module.scss';

const menuData: { label: string; children: LinkConfig[] }[] = [
  {
    label: 'Products',
    children: [
      ...ProductsSubMenus.slice(0, 1),
      {
        label: (
          <span className="flex items-center">
            <span className={styles.plusIcon}>+</span> {ProductsSubMenus[1].label}
          </span>
        ),
        href: ProductsSubMenus[1].href,
        target: ProductsSubMenus[1].target
      },
      ...ProductsSubMenus.slice(2)
    ]
  },
  {
    label: 'Developers',
    children: DevelopersSubMenu
  },
  {
    label: 'Resources',
    children: ResourcesSubMenu
  },
  {
    label: 'Contacts',
    children: ContractsSubMenu
  },
  {
    label: 'Community',
    children: CommunitySubMenu
  }
];

const languages = [
  { code: 'en-US', label: 'English' },
  { code: 'zh-TC', label: '繁体中文' },
  { code: 'zh-CN', label: '简体中文' }
];

interface MobileMenuProps {
  open: boolean;
  onClose: () => void;
  currentLang: string;
  onLangChange: (v: { label: string; code: string }) => void;
  showSwitchChain: boolean;
}

// Accordion 动画子组件
const Accordion: React.FC<{ open: boolean; children: React.ReactNode }> = ({ open, children }) => {
  const ref = useRef<HTMLDivElement>(null);
  const [height, setHeight] = useState<number | 'auto'>(open ? 'auto' : 0);

  useLayoutEffect(() => {
    const el = ref.current;
    if (!el) return;
    if (open) {
      // 展开：先设为具体高度，动画后设为auto
      setHeight(el.scrollHeight);
      const timer = setTimeout(() => setHeight('auto'), 300);
      return () => clearTimeout(timer);
    } else {
      // 收起：先设为当前高度，再设为0
      if (el.style.height === '' || el.style.height === 'auto') {
        setHeight(el.scrollHeight);
        setTimeout(() => setHeight(0), 10);
      } else {
        setHeight(0);
      }
    }
  }, [open]);

  return (
    <div
      ref={ref}
      style={{
        overflow: 'hidden',
        height: height === 'auto' ? 'auto' : `${height}px`,
        transition: 'height 0.3s cubic-bezier(0.4,0,0.2,1)'
      }}
    >
      {children}
    </div>
  );
};
function isDescendant(parent?: Node, child?: Node): boolean {
  let node: Node | null = child || null;
  while (node) {
    if (node === parent) return true;
    node = node.parentNode as Node | null;
  }
  return false;
}
const MobileMenu: React.FC<MobileMenuProps> = ({ open, onClose, currentLang, onLangChange, showSwitchChain }) => {
  const [opened, setOpened] = useState('');

  const handleMenuClick = (label: string) => {
    setOpened(prev => (prev === label ? '' : label));
  };

  const handleMenuButtonClick = () => {
    window.gtag && window.gtag('event', 'H5_header_menubutton');
  };
  const handleCloseButtonClick = () => {
    window.gtag && window.gtag('event', 'H5_header_closebutton');
    onClose();
  };

  const drawerRef = useRef(null);

  const onClickAway = useCallback(
    function (event: Event) {
      if (!drawerRef.current || !event?.target) {
        return;
      }
      if (!isDescendant(drawerRef.current, event?.target as Node)) {
        onClose();
      }
    },
    [onClose]
  );
  useEffect(() => {
    if (open) {
      document.body.addEventListener('click', onClickAway);
    }

    return () => {
      document.body.removeEventListener('click', onClickAway);
    };
  }, [onClickAway, open]);

  const env = import.meta.env.VITE_APP_ENV || '';

  return (
    <div className={classnames(styles.drawer, { [styles.open]: open })}>
      <div className={styles.header}>
        <span className={styles.logo}></span>
        <span className={styles.close} onClick={handleCloseButtonClick}></span>
      </div>
      <div className={styles.menuList}>
        {menuData.map(menu => (
          <div key={menu.label} className={styles.menuItem}>
            <div
              className={styles.menuTitle}
              onClick={() => {
                handleMenuClick(menu.label);
                window.gtag && window.gtag('event', `H5_header_${menu.label.toLowerCase()}`);
              }}
            >
              {menu.label}
              <span
                className={classnames(styles.arrow, {
                  [styles.expanded]: opened === menu.label
                })}
              >
                <svg width="8" height="7" viewBox="0 0 8 7" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M4.86603 6.5C4.48113 7.16667 3.51887 7.16667 3.13397 6.5L0.535899 2C0.150999 1.33333 0.632124 0.5 1.40192 0.5L6.59808 0.500001C7.36788 0.500001 7.849 1.33333 7.4641 2L4.86603 6.5Z"
                    fill="white"
                    fillOpacity="0.6"
                  />
                </svg>
              </span>
            </div>
            <Accordion open={opened === menu.label}>
              <div className={styles.subMenu + ' ' + (opened === menu.label ? styles.show : '')}>
                {menu.children.map(child => (
                  <a
                    key={typeof child.label === 'string' ? child.label : 'join'}
                    href={child.href}
                    target={child.target || '_self'}
                    className={styles.subMenuItem}
                    onClick={() =>
                      child.event &&
                      window.gtag &&
                      window.gtag('event', `H5_header_${child.event.replace('home_header_', '')}`)
                    }
                  >
                    {child.label}
                  </a>
                ))}
              </div>
            </Accordion>
          </div>
        ))}
      </div>

      <div className={styles.langSection}>
        {showSwitchChain && env === 'test' && (
          <div className={commonStyles.footMenuItem}>
            <span className="label">{intl.get('switch_chain')}</span>
            <SwitchChain />
          </div>
        )}
        <div className={styles.langOptions}>
          {languages.map((lang, index) => (
            <React.Fragment key={lang.code}>
              {index > 0 ? '/' : ''}
              <span
                className={classnames(styles.langOption, {
                  [styles.active]: currentLang === lang.code
                })}
                onClick={() => onLangChange(lang)}
              >
                {lang.label}
              </span>
            </React.Fragment>
          ))}
        </div>
      </div>
    </div>
  );
};

export default MobileMenu;
