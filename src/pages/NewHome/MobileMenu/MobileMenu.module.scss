.drawer {
  position: fixed;
  // height: 520px;
  // overflow-y: auto;
  top: -15px;
  left: 0;
  right: 0;
  // bottom: 0;
  background: #131121;
  z-index: 9999;
  transform: translateY(-100%);
  transition: transform 0.3s;
  padding: 15px 0 0;
  color: #fff;
}
.open {
  transform: translateY(0);
}
.header {
  display: flex;
  align-items: center;
  height: 24px;
  padding: 24px 24px 10px;
  .logo {
    width: 24px;
    height: 24px;
    background: url('../../../assets/imgs/m/logo.svg') no-repeat center/contain;
  }
  .close {
    display: block;
    margin-left: auto;
    width: 24px;
    height: 24px;
    background: url(../images/m/close.png) no-repeat center/contain;
  }
}
.menuList {
  padding: 0px 0 0 0;
  // max-height: 400px;
  overflow: auto;
}
.menuItem {
  & + .menuItem {
    margin-top: 40px;
  }
  .menuTitle {
    font-size: 14px;
    font-weight: 600;
    justify-content: center;

    padding: 0px 24px;
    cursor: pointer;
    display: flex;
    align-items: center;
  }
  .arrow {
    margin-left: 5px;
    display: flex;
    width: 10px;
    height: 10px;
    transition: transform 0.2s;
    align-items: center;
    justify-content: center;
  }
  .expanded {
    transform: rotate(180deg);
  }
  .subMenu {
    overflow: hidden;
    transition: height 0.4s cubic-bezier(0.4, 0, 0.2, 1);

    background: none;
    text-align: center;
    .subMenuItem {
      display: flex;
      padding: 0px 40px;
      color: white;
      text-decoration: none;
      justify-content: center;
      align-content: center;
      &:first-child {
        margin-top: 20px;
      }
      & + .subMenuItem {
        margin-top: 23px;
      }
      &,
      span {
        font-size: 12px;
      }
    }
  }
}
.langSection {
  border-top: 1px solid #222;
  margin-top: 32px;
  padding: 16px 20px 20px 20px;
  text-align: center;
  .langLabel {
    color: #888;
    font-size: 14px;
    margin-bottom: 8px;
    display: block;
  }
  .langOptions {
    .langOption {
      display: inline-block;
      width: 60px;
      margin: 0 8px;
      cursor: pointer;
      color: #fff;
      &.active {
        color: #00ff99;
        font-weight: bold;
      }
    }
  }
}
.plusIcon {
  color: white;
  display: block;
  width: 16px;
  margin-right: 4px;
  line-height: 16px;
  font-weight: bold;
  border-radius: 4px;
  background: rgba(51, 255, 169, 0.7);
}

@media screen and (min-width: 798px) {
  .drawer {
    display: none;
  }
}
