import { useEffect, useRef } from 'react';
import lottie from 'lottie-web';
import styles from './heroSection.module.scss';

interface LottieBackgroundProps {
  animationData: any; // lottie JSON 数据
  className?: string;
  loop?: boolean;
  autoplay?: boolean;
  speed?: number;
  style?: React.CSSProperties;
}

const LottieBackground: React.FC<LottieBackgroundProps> = ({
  animationData,
  className,
  loop = true,
  autoplay = true,
  speed = 1,
  style
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const animationRef = useRef<any>(null);

  useEffect(() => {
    if (containerRef.current && animationData) {
      // 销毁之前的动画实例
      if (animationRef.current) {
        animationRef.current.destroy();
      }

      // 创建新的动画实例
      animationRef.current = lottie.loadAnimation({
        container: containerRef.current,
        renderer: 'svg',
        loop,
        autoplay,
        animationData,
        rendererSettings: {
          preserveAspectRatio: 'xMidYMid slice'
        }
      });

      // 设置动画速度
      if (speed !== 1) {
        animationRef.current.setSpeed(speed);
      }

      // 清理函数
      return () => {
        if (animationRef.current) {
          animationRef.current.destroy();
        }
      };
    }
  }, [animationData, loop, autoplay, speed]);

  return <div ref={containerRef} className={`${styles.lottieBackground} ${className || ''}`} style={style} />;
};

export default LottieBackground;
