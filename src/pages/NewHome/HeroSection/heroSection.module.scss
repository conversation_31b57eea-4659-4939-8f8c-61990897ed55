.heroSection {
  padding: 0;
  width: 100%;
  height: 1000px;
  margin: 0 auto;
  overflow: visible;
  background: #000000;
  display: flex;
  position: relative;
}

.contentWrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
}

.tiltWrapper {
  width: 100%;
  height: 100%;
  transform-style: preserve-3d !important;
  will-change: transform;
  transform-origin: center center;
}

.bgImage {
  position: absolute;
  left: 50%;
  top: 65%;
  width: 1440px;
  height: 1430px;
  background-image: url('../images/hero-gasfree-bg.png');
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 1;
  transform: translate(-50%, -50%);
  z-index: 1;
}

.lottieBackground {
  position: absolute;
  left: 50%;
  top: 65%;
  width: 1440px;
  height: 1430px;
  opacity: 1;
  transform: translate(-50%, -50%);
  z-index: 1;

  // 确保 SVG 填充整个容器
  svg {
    width: 100%;
    height: 100%;
  }
}

.container {
  position: relative;
  z-index: 3;
  width: 1200px;
  margin: 0 auto;
  padding: 98px 0 0;
  display: grid;
  grid-template-columns: minmax(500px, 1fr) minmax(300px, auto);
  grid-template-areas: 'left right';
  align-items: flex-start;
  transform-style: preserve-3d;
}

// 左侧标题内容
.leftContent {
  grid-area: left;
  text-align: left;
  transform-style: preserve-3d !important;
  will-change: transform;

  .mainTitle {
    margin: 0 0 24px;
    text-align: left;

    .titleLine1,
    .titleLine2 {
      display: block;
      color: #ffffff;
      font-family: 'Sora', sans-serif;
      font-weight: 800;
      font-size: 50px;
      line-height: 110%;
      letter-spacing: -0.02em;
    }

    .titleLine3 {
      display: inline-block;
      background: linear-gradient(90deg, #21cffb, #33ffa9, #21cffb);
      animation: gradientAnimation 5s linear infinite;
      background-position-x: 0;
      background-position-y: center;
      background-size: 200% auto;
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      font-family: 'Sora', sans-serif;
      font-weight: 800;
      font-size: 50px;
      line-height: 110%;
      letter-spacing: -0.02em;
      margin-top: 4px;
    }
  }

  .description {
    margin: 0 0 48px;
    font-family: 'Sora';
    font-weight: 400;
    font-size: 14px;
    line-height: 150%;
    color: rgba(255, 255, 255, 0.5);
    max-width: 390px;
  }

  .buttonGroup {
    display: flex;
    gap: 16px;

    .primaryButton {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 10px 0px;
      background: rgba(51, 255, 169, 1);
      width: 160px;
      height: 40px;
      border: none;
      border-radius: 8px;
      font-family: 'Sora', sans-serif;
      font-weight: 600;
      font-size: 14px;
      color: #000000;
      cursor: pointer;
      white-space: nowrap;
      text-align: center;
      text-decoration: none;
      position: relative;
      overflow: hidden;

      // Calypso 按钮效果实现
      span {
        display: block;
        position: relative;
        z-index: 10;
        transition: color 0.4s ease;
        opacity: 1;
      }

      &::before {
        content: '';
        position: absolute;
        background: #fff;
        width: 120%;
        height: 0;
        padding-bottom: 120%;
        top: -110%;
        left: -10%;
        border-radius: 50%;
        transform: translate3d(0, 68%, 0) scale3d(0, 0, 0);
      }

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #fff;
        transform: translate3d(0, -100%, 0);
        transition: transform 0.4s cubic-bezier(0.1, 0, 0.3, 1);
      }

      &:focus {
        outline: none;
      }
      &:hover {
        &::before {
          transform: translate3d(0, 0, 0) scale3d(1, 1, 1);
          transition: transform 0.4s cubic-bezier(0.1, 0, 0.3, 1);
        }

        &::after {
          transform: translate3d(0, 0, 0);
          transition-duration: 0.05s;
          transition-delay: 0.4s;
          transition-timing-function: linear;
        }

        span {
          color: #000;
          animation:
            moveScaleUpInitial 0.3s forwards,
            moveScaleUpEnd 0.3s forwards 0.3s;
        }
      }
    }

    .secondaryButton {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 10px 24px;
      width: 160px;
      height: 40px;
      background: rgba(255, 255, 255, 0.2);
      border: none;
      border-radius: 8px;
      font-family: 'Sora', sans-serif;
      font-weight: 600;
      font-size: 14px;
      color: rgba(51, 255, 169, 1);
      cursor: pointer;
      white-space: nowrap;
      text-decoration: none;
      position: relative;
      overflow: hidden;

      // Calypso 按钮效果实现
      span {
        display: block;
        position: relative;
        z-index: 10;
        transition: color 0.4s ease;
        opacity: 1;
      }

      &::before {
        content: '';
        position: absolute;
        background: #fff;
        width: 120%;
        height: 0;
        padding-bottom: 120%;
        top: -110%;
        left: -10%;
        border-radius: 50%;
        transform: translate3d(0, 68%, 0) scale3d(0, 0, 0);
      }

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #fff;
        transform: translate3d(0, -100%, 0);
        transition: transform 0.4s cubic-bezier(0.1, 0, 0.3, 1);
      }

      &:focus {
        outline: none;
      }
      &:hover {
        &::before {
          transform: translate3d(0, 0, 0) scale3d(1, 1, 1);
          transition: transform 0.4s cubic-bezier(0.1, 0, 0.3, 1);
        }

        &::after {
          transform: translate3d(0, 0, 0);
          transition-duration: 0.05s;
          transition-delay: 0.4s;
          transition-timing-function: linear;
        }

        span {
          color: #000;
          animation:
            moveScaleUpInitial 0.3s forwards,
            moveScaleUpEnd 0.3s forwards 0.3s;
        }
      }
    }
  }
}
.leftContent {
  position: relative;
  z-index: 2;
}

// 右侧统计数据
.rightStats {
  grid-area: right;
  text-align: left;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-self: flex-start;
  padding-top: 0;
  transform-style: preserve-3d !important;
  will-change: transform;

  .tooltipContent {
    width: 260px;
    padding: 10px;
    .contentItem {
      display: flex;
      align-items: center;
      .label {
        color: rgba(255, 255, 255, 0.4);
        font-size: 12px;
      }
      .value {
        color: #fff;
        font-size: 12px;
        margin-left: 3px;
      }
    }
  }
}

.statItem {
  text-align: left;

  .statLabel {
    display: flex;
    align-items: center;
    color: rgba(255, 255, 255, 0.5);
    font-family: 'Sora';
    font-size: 14px;
    font-weight: 400;
    line-height: 100%;
    letter-spacing: 0;
    text-transform: capitalize;
    margin-bottom: 8px;
  }

  .statValue {
    display: block;
    color: #ffffff;
    font-family: 'Sora';
    font-size: 40px;
    font-weight: 700;
    line-height: 100%;
    letter-spacing: -0.02em;
  }

  .question {
    display: inline-block;
    margin-left: 5px;
    width: 16px;
    height: 16px;
    background: url(../images/question.svg) no-repeat center/contain;
  }
}

// 背景效果
.backgroundEffects {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  pointer-events: none;

  .gradientBlur1 {
    position: absolute;
    top: 20%;
    left: 10%;
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(0, 255, 136, 0.15) 0%, transparent 70%);
    border-radius: 50%;
    filter: blur(80px);
    animation: float1 6s ease-in-out infinite;
  }

  .gradientBlur2 {
    position: absolute;
    bottom: 20%;
    right: 15%;
    width: 250px;
    height: 250px;
    background: radial-gradient(circle, rgba(0, 204, 255, 0.12) 0%, transparent 70%);
    border-radius: 50%;
    filter: blur(60px);
    animation: float2 8s ease-in-out infinite;
  }
}

// 动画
@keyframes float1 {
  0%,
  100% {
    transform: translate(0, 0) scale(1);
  }
  50% {
    transform: translate(20px, -30px) scale(1.1);
  }
}

@keyframes float2 {
  0%,
  100% {
    transform: translate(0, 0) scale(1);
  }
  50% {
    transform: translate(-15px, 25px) scale(0.9);
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .heroSection {
    .container {
      grid-template-columns: 1fr;
      grid-template-areas:
        'center'
        'left'
        'right';
      gap: 40px;
      text-align: center;
    }

    .leftContent {
      .mainTitle {
        .titleLine1,
        .titleLine2,
        .titleLine3 {
          font-size: 42px;
        }
      }
    }

    .centerLogo .logo3D {
      font-size: 150px;
    }

    .rightStats {
      align-items: center;

      .statItem {
        text-align: center;
      }
    }
  }
}

@media (max-width: 768px) {
  .heroSection {
    padding: 30px 20px 70px;
    width: unset;
    height: unset;

    .bgImage {
      background: url(../images/m/hero-bg.png) no-repeat center top/auto;
      width: 100vw;
      height: 800px;
      top: 115px; // paddingtop 30 + title height 85
      left: 0;
      transform: none;
    }

    .container {
      padding: 0;
      display: flex;
      flex-direction: column;
    }
    .leftContent {
      .mainTitle {
        font-size: 24px;
        margin-bottom: 10px;
        .titleLine1,
        .titleLine2,
        .titleLine3 {
          font-size: 24px;
          font-weight: 800;
        }
      }
      .description {
        font-size: 11px;
        margin-bottom: 10px;
      }

      .buttonGroup {
        flex-direction: row;
        align-items: center;
        margin-bottom: 191px;

        .primaryButton,
        .secondaryButton {
          // width: 200px;
          font-size: 14px;
          line-height: 30px;
          font-weight: 600;
          height: 30px;
          padding: 0;
          border-radius: 6px;
          &:focus {
            opacity: 0.8;
          }
        }
      }
    }
    .rightStats {
      width: unset;
      display: flex;
      flex-wrap: wrap;
      flex-direction: row;
      align-items: start;

      .statItemWrap {
        flex: 1 1 auto;
        width: 40%;
        margin-bottom: 0;

        &:first-child {
          margin-right: 20px;
        }
        &:last-child {
          margin-top: 50px;
        }
      }

      .statItem {
        .statLabel {
          font-size: 12px;
          text-align: left;
          margin-bottom: 10px;
        }
        .statValue {
          font-size: 20px;
          font-weight: 700;
          text-align: left;
        }
      }

      .tooltipContent {
        width: 260px;
        padding: 10px 10px 10px 15px;
        .contentItem {
          display: flex;
          align-items: center;
          line-height: 15px;

          .label {
            color: rgba(255, 255, 255, 0.4);
            font-size: 11px;
          }
          .value {
            color: #fff;
            font-size: 11px;
            margin-left: 3px;
          }
        }
      }
    }

    .centerLogo .logo3D {
      font-size: 120px;
    }
  }
}

// 文字颜色渐变滚动
@keyframes gradientAnimation {
  0% {
    background-position: 100% center;
  }
  50% {
    background-position: 0% center;
  }
  100% {
    background-position: -100% center;
  }
}

// Calypso 按钮文字动画
@keyframes moveScaleUpInitial {
  to {
    transform: translate3d(0, -100%, 0) scale3d(1, 1.5, 1);
    opacity: 0;
  }
}

@keyframes moveScaleUpEnd {
  from {
    transform: translate3d(0, 100%, 0) scale3d(1, 1.5, 1);
    opacity: 0;
  }
  to {
    transform: translate3d(0, 0, 0) scale3d(1, 1, 1);
    opacity: 1;
  }
}
