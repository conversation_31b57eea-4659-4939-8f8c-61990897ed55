import React, { useState, useEffect } from 'react';
import styles from './heroSection.module.scss';
import { useTypeShuffle } from '../hooks/useTypeShuffle';
import { useIsMobile } from '@/hooks/useIsMobile';
import classNames from 'classnames';
import scrollRevealStyles from '../components/ScrollReveal/ScrollReveal.module.scss';
import { ScrollReveal as AnimatedWrapper } from '../components/ScrollReveal/ScrollReveal';
import Tilt from 'react-parallax-tilt';
import Tooltip from '../components/Tooltip/Tooltip';
import intl from 'react-intl-universal';
import { getStatsHistorySummary } from '../../../utils/backend';
import LottieBackground from './LottieBackground';
import heroAnimationData from './hero-animation.json';

// 定义统计数据的类型
interface StatsData {
  volume: number;
  count: number;
  savings: number;
  volumeFormatted: string;
  countFormatted: string;
  savingsFormatted: string;
}

// StatItem 组件
const StatItem: React.FC<{
  label: string;
  value: string;
  delay: number;
  tooltip?: React.ReactNode;
}> = ({ label, value, delay, tooltip }) => {
  const [valueRef] = useTypeShuffle({
    text: value,
    delay,
    duration: 2500,
    autoStart: true
  });

  return (
    <AnimatedWrapper
      delay={delay}
      duration={0.8}
      distance="50px"
      direction="right"
      parallax={true}
      parallaxSpeed={0.8}
      className={classNames(scrollRevealStyles.statSpacing, styles.statItemWrap)}
    >
      <div className={styles.statItem}>
        <div className={styles.statLabel}>
          {label}
          {tooltip}
        </div>
        <div className={styles.statValueOuter}>
          <div className={styles.statValueWrapper}>
            <span ref={valueRef} />
          </div>
        </div>
      </div>
    </AnimatedWrapper>
  );
};

const HeroSection: React.FC = () => {
  const isMobile = useIsMobile();
  const [statsData, setStatsData] = useState<StatsData>({
    volume: 0,
    count: 0,
    savings: 0,
    volumeFormatted: '',
    countFormatted: '',
    savingsFormatted: ''
  });

  // 示例 Lottie 动画数据 - 你可以替换成你的 JSON 文件
  const [lottieAnimationData, setLottieAnimationData] = useState<any>(null);
  const [useLottie, setUseLottie] = useState(false);

  const [volumeRef] = useTypeShuffle({
    text: statsData.volumeFormatted,
    delay: 500,
    duration: 2500,
    autoStart: true
  });

  const [countRef] = useTypeShuffle({
    text: statsData.countFormatted,
    delay: 1000,
    duration: 2500,
    autoStart: true
  });

  const [savingsRef] = useTypeShuffle({
    text: statsData.savingsFormatted,
    delay: 1500,
    duration: 2500,
    autoStart: true
  });

  const [isLoading, setIsLoading] = useState(false);

  // 加载 Lottie 动画数据
  useEffect(() => {
    try {
      // 直接使用导入的动画数据
      setLottieAnimationData(heroAnimationData);
      setUseLottie(true);
    } catch (error) {
      console.error('加载 Lottie 动画失败:', error);
      setUseLottie(false);
    }
  }, []);

  // 获取统计数据
  useEffect(() => {
    let isMounted = true;

    const fetchStatsData = async () => {
      if (!isMounted) return;

      try {
        setIsLoading(true);
        const result = await getStatsHistorySummary();

        if (!isMounted) return;

        if (result.success && result.data) {
          const apiData = result.data;

          // 计算数值
          const volume = Math.floor(apiData.data.totalTransferAmount / 1e6);
          const count = Math.floor(apiData.data.totalTransferCount);
          const savings = Math.floor(count * 2.93);

          // 更新状态
          setStatsData({
            volume,
            count,
            savings,
            volumeFormatted: `$${volume.toLocaleString()}+`,
            countFormatted: `${count.toLocaleString()}+`,
            savingsFormatted: `$${savings.toLocaleString()}+`
          });
        }
      } catch (error) {
        console.error('获取统计数据失败:', error);
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    fetchStatsData();

    return () => {
      isMounted = false;
    };
  }, []);

  return isMobile ? (
    <section className={styles.heroSection}>
      {useLottie && lottieAnimationData ? (
        <LottieBackground animationData={lottieAnimationData} loop={true} autoplay={true} speed={0.8} />
      ) : (
        <div className={styles.bgImage} />
      )}
      <div className={styles.container}>
        {/* 左侧内容区域 */}
        <AnimatedWrapper delay={200} duration={0.8} distance="60px" direction="up" parallax={true} parallaxSpeed={0.7}>
          <div className={styles.leftContent}>
            <AnimatedWrapper delay={200} duration={0.8} distance="60px" direction="up">
              <h1 className={styles.mainTitle}>
                <span className={styles.titleLine1}>{intl.get('newhome_hero_title1')}</span>
                <span className={styles.titleLine2}>{intl.get('newhome_hero_title2')}</span>
                <span className={styles.titleLine3}>{intl.get('newhome_hero_title3')}</span>
              </h1>
            </AnimatedWrapper>

            <AnimatedWrapper delay={400} duration={0.8} distance="40px" direction="up">
              <p className={styles.description}>{intl.get('newhome_hero_desc')}</p>
            </AnimatedWrapper>

            <AnimatedWrapper delay={600} duration={0.8} distance="30px" direction="up">
              <div className={styles.buttonGroup}>
                <a
                  href="/3rdparty"
                  className={styles.primaryButton}
                  onClick={() => window.gtag && window.gtag('event', 'home_body_3rdparty')}
                >
                  <span>{intl.get('newhome_hero_wallets')}</span>
                </a>
                <a
                  href="https://developer.gasfree.io/register"
                  target="_blank"
                  rel="noopener noreferrer"
                  className={styles.secondaryButton}
                  onClick={() => window.gtag && window.gtag('event', 'home_body_integrate')}
                >
                  <span>{intl.get('newhome_hero_integrate')}</span>
                </a>
              </div>
            </AnimatedWrapper>
          </div>
        </AnimatedWrapper>

        {/* 右侧统计数据 */}
        {statsData.volumeFormatted && (
          <div className={styles.rightStats}>
            <AnimatedWrapper
              delay={300}
              duration={0.8}
              distance="50px"
              direction="right"
              parallax={true}
              parallaxSpeed={0.8}
              className={classNames(scrollRevealStyles.statSpacing, styles.statItemWrap)}
            >
              <div className={styles.statItem}>
                <span className={styles.statLabel}>{intl.get('newhome_hero_volume')}</span>
                <span ref={volumeRef} className={styles.statValue}>
                  {statsData.volumeFormatted}
                </span>
              </div>
            </AnimatedWrapper>

            <AnimatedWrapper
              delay={500}
              duration={0.8}
              distance="50px"
              direction="right"
              parallax={true}
              parallaxSpeed={0.85}
              className={classNames(scrollRevealStyles.statSpacing, styles.statItemWrap)}
            >
              <div className={styles.statItem}>
                <span className={styles.statLabel}>{intl.get('newhome_hero_count')}</span>
                <span ref={countRef} className={styles.statValue}>
                  {statsData.countFormatted}
                </span>
              </div>
            </AnimatedWrapper>

            <AnimatedWrapper
              delay={700}
              duration={0.8}
              distance="50px"
              direction="right"
              parallax={true}
              parallaxSpeed={0.9}
              className={classNames(scrollRevealStyles.statSpacing, styles.statItemWrap)}
            >
              <div className={styles.statItem}>
                <span className={styles.statLabel}>
                  {intl.get('newhome_hero_fees')}
                  <Tooltip
                    content={
                      <div className={styles.tooltipContent}>
                        <div className={styles.contentItem}>
                          <span className={styles.label}>{intl.get('newhome_hero_label_fee')}</span>
                          <span className={styles.value}>$1 {intl.get('newhome_per_Txn')}</span>
                        </div>
                        <div className={styles.contentItem}>
                          <span className={styles.label}>{intl.get('newhome_hero_label_fee_traditional')}</span>
                          <span className={styles.value}>~$3.93 {intl.get('newhome_per_Txn')}</span>
                        </div>
                      </div>
                    }
                  >
                    <span className={styles.question}></span>
                  </Tooltip>
                </span>
                <span ref={savingsRef} className={styles.statValue}>
                  {statsData.savingsFormatted}
                </span>
              </div>
            </AnimatedWrapper>
          </div>
        )}
      </div>

      {/* 背景装饰元素 */}
      <div className={styles.backgroundEffects}>
        <AnimatedWrapper
          delay={100}
          duration={1.5}
          distance="100px"
          direction="left"
          parallax={true}
          parallaxSpeed={0.5}
        >
          <div className={styles.gradientBlur1}></div>
        </AnimatedWrapper>
        <AnimatedWrapper
          delay={800}
          duration={1.5}
          distance="100px"
          direction="right"
          parallax={true}
          parallaxSpeed={0.95}
        >
          <div className={styles.gradientBlur2}></div>
        </AnimatedWrapper>
      </div>
    </section>
  ) : (
    <section className={styles.heroSection}>
      {useLottie && lottieAnimationData ? (
        <LottieBackground animationData={lottieAnimationData} loop={true} autoplay={true} speed={0.8} />
      ) : (
        <div className={styles.bgImage} />
      )}
      <div className={styles.contentWrapper}>
        <Tilt
          tiltMaxAngleX={8}
          tiltMaxAngleY={8}
          perspective={1500}
          scale={1.02}
          transitionSpeed={2500}
          tiltReverse={true}
          reset={false}
          className={styles.tiltWrapper}
          style={{ transformStyle: 'preserve-3d' }}
        >
          <div className={styles.container}>
            {/* 左侧内容区域 */}
            <AnimatedWrapper delay={0} duration={0} distance="0" direction="up" parallax={true} parallaxSpeed={0.7}>
              <div className={styles.leftContent}>
                <AnimatedWrapper delay={0} duration={0} distance="0" direction="up">
                  <h1 className={styles.mainTitle}>
                    <span className={styles.titleLine1}>{intl.get('newhome_hero_title1')}</span>
                    <span className={styles.titleLine2}>{intl.get('newhome_hero_title2')}</span>
                    <span className={styles.titleLine3}>{intl.get('newhome_hero_title3')}</span>
                  </h1>
                </AnimatedWrapper>

                <AnimatedWrapper delay={0} duration={0} distance="0" direction="up">
                  <p className={styles.description}>{intl.get('newhome_hero_desc')}</p>
                </AnimatedWrapper>

                <AnimatedWrapper delay={0} duration={0} distance="0" direction="up">
                  <div className={styles.buttonGroup}>
                    <a
                      href="/3rdparty"
                      className={styles.primaryButton}
                      onClick={() => window.gtag && window.gtag('event', 'home_body_3rdparty')}
                    >
                      <span>{intl.get('newhome_hero_wallets')}</span>
                    </a>
                    <a
                      href="https://developer.gasfree.io/register"
                      target="_blank"
                      rel="noopener noreferrer"
                      className={styles.secondaryButton}
                      onClick={() => window.gtag && window.gtag('event', 'home_body_integrate')}
                    >
                      <span>{intl.get('newhome_hero_integrate')}</span>
                    </a>
                  </div>
                </AnimatedWrapper>
              </div>
            </AnimatedWrapper>

            {/* 右侧统计数据 */}
            {statsData.volumeFormatted && (
              <div className={styles.rightStats}>
                <AnimatedWrapper
                  delay={300}
                  duration={0.8}
                  distance="50px"
                  direction="right"
                  parallax={true}
                  parallaxSpeed={0.8}
                  className={classNames(scrollRevealStyles.statSpacing, styles.statItemWrap)}
                >
                  <div className={styles.statItem}>
                    <span className={styles.statLabel}>{intl.get('newhome_hero_volume')}</span>
                    <span ref={volumeRef} className={styles.statValue}>
                      {statsData.volumeFormatted}
                    </span>
                  </div>
                </AnimatedWrapper>

                <AnimatedWrapper
                  delay={500}
                  duration={0.8}
                  distance="50px"
                  direction="right"
                  parallax={true}
                  parallaxSpeed={0.85}
                  className={classNames(scrollRevealStyles.statSpacing, styles.statItemWrap)}
                >
                  <div className={styles.statItem}>
                    <span className={styles.statLabel}>{intl.get('newhome_hero_count')}</span>
                    <span ref={countRef} className={styles.statValue}>
                      {statsData.countFormatted}
                    </span>
                  </div>
                </AnimatedWrapper>

                <AnimatedWrapper
                  delay={700}
                  duration={0.8}
                  distance="50px"
                  direction="right"
                  parallax={true}
                  parallaxSpeed={0.9}
                  className={classNames(scrollRevealStyles.statSpacing, styles.statItemWrap)}
                >
                  <div className={styles.statItem}>
                    <span className={styles.statLabel}>
                      {intl.get('newhome_hero_fees')}
                      <Tooltip
                        content={
                          <div className={styles.tooltipContent}>
                            <div className={styles.contentItem}>
                              <span className={styles.label}>{intl.get('newhome_hero_label_fee')}</span>
                              <span className={styles.value}>$1 {intl.get('newhome_per_Txn')}</span>
                            </div>
                            <div className={styles.contentItem}>
                              <span className={styles.label}>{intl.get('newhome_hero_label_fee_traditional')}</span>
                              <span className={styles.value}>~$3.93 {intl.get('newhome_per_Txn')}</span>
                            </div>
                          </div>
                        }
                      >
                        <span className={styles.question}></span>
                      </Tooltip>
                    </span>
                    <span ref={savingsRef} className={styles.statValue}>
                      {statsData.savingsFormatted}
                    </span>
                  </div>
                </AnimatedWrapper>
              </div>
            )}
          </div>
        </Tilt>
      </div>

      {/* 背景装饰元素 */}
      <div className={styles.backgroundEffects}>
        <AnimatedWrapper
          delay={100}
          duration={1.5}
          distance="100px"
          direction="left"
          parallax={true}
          parallaxSpeed={0.5}
        >
          <div className={styles.gradientBlur1}></div>
        </AnimatedWrapper>
        <AnimatedWrapper
          delay={800}
          duration={1.5}
          distance="100px"
          direction="right"
          parallax={true}
          parallaxSpeed={0.95}
        >
          <div className={styles.gradientBlur2}></div>
        </AnimatedWrapper>
      </div>
    </section>
  );
};

export { HeroSection };
