<svg width="375" height="373" viewBox="0 0 375 373" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_469_5268" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="375" height="373">
<path d="M0 21.9445C0 9.82488 9.82486 0 21.9444 0H353.056C365.175 0 375 9.82486 375 21.9444V373H0V21.9445Z" fill="url(#paint0_linear_469_5268)"/>
</mask>
<g mask="url(#mask0_469_5268)">
<g filter="url(#filter0_f_469_5268)">
<ellipse cx="189.114" cy="-40.3229" rx="214.233" ry="69.3993" fill="#1938FF" fill-opacity="0.5"/>
</g>
<g filter="url(#filter1_f_469_5268)">
<ellipse cx="189.114" cy="-40.3229" rx="214.233" ry="69.3993" fill="#1938FF" fill-opacity="0.5"/>
</g>
<g filter="url(#filter2_f_469_5268)">
<ellipse cx="189.114" cy="-40.3229" rx="103.962" ry="69.3993" fill="#1938FF" fill-opacity="0.5"/>
</g>
<g filter="url(#filter3_f_469_5268)">
<ellipse cx="189.115" cy="-14.5381" rx="67.7535" ry="38.6771" fill="#33FFA9"/>
</g>
<g filter="url(#filter4_f_469_5268)">
<ellipse cx="189.114" cy="-14.5382" rx="44.7118" ry="25.5104" fill="#37FFAB"/>
</g>
</g>
<defs>
<filter id="filter0_f_469_5268" x="-134.84" y="-219.444" width="647.909" height="358.243" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="54.8611" result="effect1_foregroundBlur_469_5268"/>
</filter>
<filter id="filter1_f_469_5268" x="-134.84" y="-219.444" width="647.909" height="358.243" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="54.8611" result="effect1_foregroundBlur_469_5268"/>
</filter>
<filter id="filter2_f_469_5268" x="-24.5699" y="-219.444" width="427.368" height="358.243" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="54.8611" result="effect1_foregroundBlur_469_5268"/>
</filter>
<filter id="filter3_f_469_5268" x="66.5002" y="-108.076" width="245.229" height="187.076" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="27.4306" result="effect1_foregroundBlur_469_5268"/>
</filter>
<filter id="filter4_f_469_5268" x="89.5412" y="-94.9097" width="199.146" height="160.743" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="27.4306" result="effect1_foregroundBlur_469_5268"/>
</filter>
<linearGradient id="paint0_linear_469_5268" x1="187.5" y1="-44.8681" x2="187.5" y2="381.92" gradientUnits="userSpaceOnUse">
<stop offset="0.82275" stop-color="#2838BF"/>
<stop offset="1" stop-color="#33FFA9" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
