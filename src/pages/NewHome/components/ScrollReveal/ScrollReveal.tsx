import React, { useEffect, useRef, useState } from 'react';
import styles from './ScrollReveal.module.scss';

interface ScrollRevealProps {
  children: React.ReactNode;
  delay?: number;
  duration?: number;
  distance?: string;
  direction?: 'up' | 'down' | 'left' | 'right';
  threshold?: number;
  className?: string;
  once?: boolean;
  // 新增视差效果配置
  parallax?: boolean; // 是否启用视差效果
  parallaxSpeed?: number; // 视差速度，0.1-1.0，数值越小移动越慢
}

export const ScrollReveal: React.FC<ScrollRevealProps> = ({
  children,
  delay = 0,
  duration = 0.8,
  distance = '50px',
  direction = 'up',
  threshold = 0.1,
  className = '',
  once = true,
  parallax = false,
  parallaxSpeed = 0.5
}) => {
  const elementRef = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [hasBeenVisible, setHasBeenVisible] = useState(false);
  const [parallaxOffset, setParallaxOffset] = useState(0);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    // 检查元素是否已经在视口内
    const checkInitialVisibility = () => {
      const rect = element.getBoundingClientRect();
      const isVisible = rect.top < window.innerHeight && rect.bottom > 0;
      if (isVisible) {
        setTimeout(() => {
          setIsVisible(true);
          if (once) {
            setHasBeenVisible(true);
          }
        }, delay);
      }
    };

    // 页面加载完成后检查初始可见性
    if (document.readyState === 'complete') {
      checkInitialVisibility();
    } else {
      window.addEventListener('load', checkInitialVisibility);
    }

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setTimeout(() => {
            setIsVisible(true);
            if (once) {
              setHasBeenVisible(true);
            }
          }, delay);
        } else if (!once && !hasBeenVisible) {
          setIsVisible(false);
        }
      },
      {
        threshold,
        rootMargin: '100px 0px -100px 0px'
      }
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
      window.removeEventListener('load', checkInitialVisibility);
    };
  }, [delay, threshold, once, hasBeenVisible]);

  // 视差滚动效果
  useEffect(() => {
    if (!parallax) return;

    let ticking = false;

    const updateParallax = () => {
      // 查找滚动容器 - 优先使用 newHomeContainer
      const scrollContainer = document.querySelector('[class*="newHomeContainer"]') as HTMLElement;
      const scrollY = scrollContainer ? scrollContainer.scrollTop : window.scrollY;
      const offset = scrollY * (parallaxSpeed - 1);
      setParallaxOffset(offset);

      ticking = false;
    };

    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(updateParallax);
        ticking = true;
      }
    };

    // 找到正确的滚动容器
    const scrollContainer = document.querySelector('[class*="newHomeContainer"]') as HTMLElement;
    const scrollTarget = scrollContainer || window;

    scrollTarget.addEventListener('scroll', handleScroll, { passive: true });

    // 初始计算
    updateParallax();

    return () => {
      scrollTarget.removeEventListener('scroll', handleScroll);
    };
  }, [parallax, parallaxSpeed]);

  const getTransform = () => {
    switch (direction) {
      case 'up':
        return `translateY(${distance})`;
      case 'down':
        return `translateY(-${distance})`;
      case 'left':
        return `translateX(${distance})`;
      case 'right':
        return `translateX(-${distance})`;
      default:
        return `translateY(${distance})`;
    }
  };

  const animationStyle = {
    '--scroll-reveal-duration': `${duration}s`,
    '--scroll-reveal-delay': `${delay}ms`,
    '--scroll-reveal-transform': getTransform()
  } as React.CSSProperties;

  return (
    <div
      ref={elementRef}
      className={`${styles.scrollReveal} ${isVisible ? styles.visible : ''} ${className}`}
      style={animationStyle}
    >
      {parallax ? (
        // 视差启用时，用内层div处理视差效果
        <div
          style={{
            transform: `translateY(${parallaxOffset}px)`,
            transition: 'none'
          }}
        >
          {children}
        </div>
      ) : (
        children
      )}
    </div>
  );
};
