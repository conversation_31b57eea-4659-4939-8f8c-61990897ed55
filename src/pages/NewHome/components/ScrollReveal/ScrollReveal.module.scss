.scrollReveal {
  opacity: 0;
  transform: var(--scroll-reveal-transform);
  transition:
    opacity var(--scroll-reveal-duration) cubic-bezier(0.25, 0.46, 0.45, 0.94),
    transform var(--scroll-reveal-duration) cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transition-delay: var(--scroll-reveal-delay);

  &.visible {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }

  // 为统计数据添加间距
  &.statSpacing {
    margin-bottom: 80px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

// 预设动画效果
.scrollReveal {
  // 向上滑入
  &.slideInUp {
    transform: translateY(50px);
  }

  // 向下滑入
  &.slideInDown {
    transform: translateY(-50px);
  }

  // 向左滑入
  &.slideInLeft {
    transform: translateX(50px);
  }

  // 向右滑入
  &.slideInRight {
    transform: translateX(-50px);
  }

  // 放大进入
  &.scaleIn {
    transform: scale(0.8);
  }

  // 淡入
  &.fadeIn {
    transform: none;
  }

  // 旋转进入
  &.rotateIn {
    transform: rotate(-10deg) scale(0.8);
  }
}

// 特殊效果
.scrollReveal {
  &.bounce {
    transition-timing-function: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }

  &.elastic {
    transition-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1.275);
  }

  &.smooth {
    transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }
}
