.wrapper {
  position: relative;
  display: inline-block;
}

.tooltip {
  position: absolute;
  left: 50%;
  bottom: 100%;
  transform: translateX(-50%) translateY(-8px) scale(0.98);
  opacity: 0;
  pointer-events: none;
  background: url(../../images/tooltip-content.svg) no-repeat center/contain;
  background-position: 1px center;
  color: #fff;
  padding: 0 0px 8px 0px;
  border-radius: 6px;
  white-space: pre-line;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.14);
  transition:
    opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1),
    transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 10;
  margin-bottom: 5px;
  margin-left: 1px;
}

.visible {
  opacity: 1;
  pointer-events: auto;
  transform: translateX(-50%) translateY(0) scale(1);
}

.arrow {
  position: absolute;
  bottom: 3px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid rgba(0, 0, 0, 0.8);
}

@media screen and (max-width: 798px) {
  .tooltip {
    left: 100%;
    top: 50%;
    bottom: unset;
    transform: translateX(0px) translateY(-50%) scale(0.98);
    background: url(../../images/m/tooltip-content.svg) no-repeat 0 center/contain;
    padding-bottom: 0;
    padding-left: 0px;
    margin-left: 5px;
  }

  .arrow {
    left: 0;
    top: 50%;
    bottom: unset;
    transform: translateY(-50%) rotate(-90deg);
    margin-left: -2px;
  }
}
