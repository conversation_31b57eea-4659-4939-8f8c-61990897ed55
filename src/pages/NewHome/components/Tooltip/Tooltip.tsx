import React, { useState } from 'react';
import styles from './Tooltip.module.css';

interface TooltipProps {
  content: React.ReactNode;
  children: React.ReactNode;
  className?: string;
  wrapperClassName?: string;
}

const Tooltip: React.FC<TooltipProps> = ({ content, children, className = '', wrapperClassName = '' }) => {
  const [visible, setVisible] = useState(false);

  return (
    <span
      className={`${styles.wrapper} ${wrapperClassName}`}
      onMouseEnter={() => setVisible(true)}
      onMouseLeave={() => setVisible(false)}
    >
      {children}
      <span className={`${styles.tooltip} ${visible ? styles.visible : ''} ${className}`}>
        {content}
        {/* <span className={styles.arrow} /> */}
      </span>
    </span>
  );
};

export default Tooltip;
