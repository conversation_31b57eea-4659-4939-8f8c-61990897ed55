.footer {
  width: 100%;
  max-width: 1242px;
  margin: 0 auto;
  padding: 100px 60px 0;
  background: rgba(0, 0, 0, 0.5);
  fill: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(20px);
  background-size: contain;
  background-position: center center;
  background-repeat: no-repeat;
  position: relative;
  z-index: 2;
  border-radius: 40px;

  .container {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 40px;
    margin-bottom: 83px;
    text-align: left;
  }

  .logoColumn {
    .logo {
      height: 40px;
      margin-bottom: 24px;
      display: block; // 确保logo也是左对齐
    }

    .socialIcons {
      display: flex;
      gap: 30px;
      margin-bottom: 24px;

      .socialIcon {
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: rgba(255, 255, 255, 0.5);
        transition: all 0.3s ease;
        border-radius: 8px;

        img {
          width: 20px;
          height: 20px;
          transition: opacity 0.3s ease;
          opacity: 0.6;
        }

        &:hover img {
          opacity: 1;
        }
      }
    }

    .copyright {
      font-family: 'Sora';
      font-size: 14px;
      color: rgba(255, 255, 255, 0.5);
      text-align: left;
    }
  }

  .menuColumn {
    .menuTitle {
      font-family: 'Sora';
      font-size: 16px;
      font-weight: 600;
      color: #ffffff;
      margin: 0 0 40px;
      text-align: left;
    }

    .menuList {
      list-style: none;
      padding: 0;
      margin: 0;
      text-align: left;

      .menuItem {
        margin-bottom: 25px;
        line-height: 18px;

        &:last-child {
          margin-bottom: 0;
        }

        .menuLink {
          font-family: 'Sora';
          font-size: 14px;
          color: rgba(255, 255, 255, 0.7);
          text-decoration: none;
          transition: color 0.3s ease;
          display: inline-block; // 确保链接本身也是左对齐

          &:hover {
            color: #33ffa9;
          }
        }

        .joinLink {
          display: inline-flex;
          align-items: center;
          gap: 8px;
          font-weight: 500;
          transition: all 0.3s ease;

          &::before {
            content: '+';
            width: 16px;
            height: 16px;
            background-color: rgba(51, 255, 169, 0.7);
            border-radius: 2px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            font-weight: bold;
            color: #fff;
            flex-shrink: 0;
          }

          &:hover {
            color: #33ffa9 !important;

            &::before {
              background-color: #2ee89a;
            }
          }
        }
      }
    }
  }
}

.bottomBar {
  display: flex;
  justify-content: space-between; // 改为两端对齐
  align-items: center;
  padding: 20px 0px;
  height: 60px;
  max-width: 1242px;
  margin: 0 auto;
  width: 100%;
  // border-top: 1px solid rgba(255, 255, 255, 0.1);

  .copyright {
    font-family: 'Sora', 'Wix Madefor Display', sans-serif;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    letter-spacing: 0%;
    color: rgba(255, 255, 255, 0.5);
    text-transform: capitalize;
  }

  .version {
    font-size: 14px;
    color: #fff;
    font-weight: 400;
    margin-left: 20px;
  }

  .links {
    display: flex;
    gap: 24px;

    .link {
      font-family: 'Sora';
      font-size: 14px;
      line-height: 20px;
      color: rgba(255, 255, 255, 0.5);
      text-decoration: none;
      transition: color 0.3s ease;

      &:hover {
        color: #33ffa9;
      }
    }
  }
}
/* 移动端响应式设计 */
@media (max-width: 768px) {
  .footer {
    width: 100%;
    max-width: 500px;
    margin: 0 auto;
    padding: 30px 20px;
    border-radius: 20px;

    .container {
      grid-template-columns: repeat(2, 1fr);
      gap: 40px 30px;
      margin-bottom: 40px;
      max-width: 100%;
    }

    .logoColumn {
      grid-column: span 2;
      text-align: left;
      margin-bottom: 30px;

      .logo {
        height: 32px;
        margin-bottom: 20px;
      }

      .socialIcons {
        justify-content: flex-start;
        gap: 16px;
        margin-bottom: 0;
      }
    }

    .menuColumn {
      text-align: left;

      .menuTitle {
        font-size: 14px;
        margin-bottom: 20px;
        font-weight: 600;
      }

      .menuList {
        .menuItem {
          margin-bottom: 12px;

          .menuLink {
            font-size: 13px;
          }
        }
      }
    }
  }
  .bottomBar {
    flex-direction: column;
    gap: 16px;
    text-align: left;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 0 0 20px;
    height: auto;

    .copyright {
      font-size: 12px;
      order: 1;
    }

    .version {
      font-size: 12px;
    }

    .links {
      gap: 24px;
      order: 0;

      .link {
        font-size: 12px;
      }
    }
  }
}

@media (max-width: 480px) {
  .footer {
    max-width: 90%;
    padding: 24px 16px;
    border-radius: 16px;

    .container {
      grid-template-columns: repeat(2, 1fr);
      gap: 30px 20px;
      margin-bottom: 30px;
    }

    .logoColumn {
      grid-column: span 2;
      margin-bottom: 20px;

      .logo {
        height: 28px;
        margin-bottom: 16px;
      }

      .socialIcons {
        gap: 16px;

        .socialIcon {
          width: 18px;
          height: 18px;

          img {
            width: 18px;
            height: 18px;
          }
        }
      }
    }

    .menuColumn {
      .menuTitle {
        font-size: 13px;
        margin-bottom: 16px;
      }

      .menuList {
        .menuItem {
          margin-bottom: 10px;

          .menuLink {
            font-size: 12px;
          }

          .joinLink {
            gap: 6px;

            &::before {
              width: 14px;
              height: 14px;
              font-size: 16px;
            }
          }
        }
      }
    }
  }
  .bottomBar {
    padding-top: 24px;
    gap: 12px;

    .copyright {
      font-size: 11px;
    }

    .links {
      gap: 20px;

      .link {
        font-size: 11px;
      }
    }
  }
}
