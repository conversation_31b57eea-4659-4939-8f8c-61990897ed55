import { get } from '@/utils/select-lang';
import { ReactNode } from 'react';

export interface LinkConfig {
  label: string | ReactNode;
  href: string;
  target?: '_blank' | '_self';
  event?: string;
}
export const ProductsSubMenus: LinkConfig[] = [
  { label: get('newhome_nav_3rd_party'), href: '/3rdparty', event: 'home_header_3rdparty' },
  {
    label: get('newhome_nav_join'),
    href: 'https://developer.gasfree.io/register',
    target: '_blank',
    event: 'home_header_join'
  },
  { label: get('newhome_nav_withdraw'), href: '/withdraw', event: 'home_header_withdraw' }
];

export const DevelopersSubMenu: LinkConfig[] = [
  {
    label: get('newhome_nav_docs'),
    href: '/docs/GasFree_specification.html',
    target: '_blank',
    event: 'home_header_doc'
  },
  {
    label: get('newhome_nav_dev_center'),
    href: 'https://developer.gasfree.io/',
    target: '_blank',
    event: 'home_header_devcenter'
  },
  {
    label: 'GitHub',
    href: 'https://github.com/TronLink/tronlink-gasfree-sdk-js',
    target: '_blank',
    event: 'home_header_github'
  },
  {
    label: get('newhome_nav_faucet'),
    href: 'https://nileex.io/join/getJoinPage',
    target: '_blank',
    event: 'home_header_faucets'
  }
];

export const ResourcesSubMenu: LinkConfig[] = [
  {
    label: get('newhome_nav_whitepaper'),
    href: '/gasfree_whitepaper_en.pdf',
    target: '_blank',
    event: 'home_header_whitepaper'
  },
  {
    label: get('newhome_nav_help_center'),
    href: 'https://support.tronlink.org/hc/en-us/articles/38903684778393-GasFree-User-Guide',
    target: '_blank',
    event: 'home_header_help_en'
  },
  { label: 'FAQs', href: '/faq', event: 'home_header_faq' }
];

export const ContractsSubMenu: LinkConfig[] = [
  {
    label: get('newhome_nav_technical'),
    href: 'https://docs.google.com/forms/d/e/1FAIpQLSfrj1A0mHKFtxfPQ5VY-kC56E7ZXVl6KBN6bB02uzzPsDIjPg/viewform?usp=header',
    target: '_blank',
    event: 'home_header_tech_form'
  },
  {
    label: get('newhome_nav_feedback'),
    href: 'https://docs.google.com/forms/d/e/1FAIpQLSdrJ1smqG5coYZi5-c7wtdWLEtDTTx-x_XRK0q-dUl-wzf9vw/viewform?usp=header',
    target: '_blank',
    event: 'home_header_feedback'
  }
];

export const CommunitySubMenu: LinkConfig[] = [
  { label: 'X', href: 'https://twitter.com/DeFi_JUST', target: '_blank', event: 'home_header_twitter' },
  { label: 'Telegram', href: 'https://t.me/officialjustlend', target: '_blank', event: 'home_header_telegram' },
  { label: 'Discord', href: 'https://discord.gg/2KdByBgBA3', target: '_blank', event: 'home_header_discord' }
];
