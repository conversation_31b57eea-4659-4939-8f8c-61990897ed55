/**
 * TypeShuffle - 基于原始 TypeShuffleAnimation 库的实现
 * 支持6种不同的字符混合动画效果
 */

export interface TypeShuffleOptions {
  text?: string;
  delay?: number;
  duration?: number;
  iterations?: number;
  fps?: number;
  onComplete?: () => void;
}

export class TypeShuffle {
  private element: HTMLElement;
  private originalText: string;
  private options: Required<TypeShuffleOptions>;
  private chars: string = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_+-=[]{}|;:,.<>?';
  private animationId: number | null = null;
  private isRunning: boolean = false;
  private startTime: number = 0;

  constructor(element: HTMLElement, options: TypeShuffleOptions = {}) {
    this.element = element;
    this.originalText = options.text || element.textContent || '';

    this.options = {
      text: this.originalText,
      delay: options.delay || 0,
      duration: options.duration || 2000,
      iterations: options.iterations || 10,
      fps: options.fps || 30,
      onComplete: options.onComplete || (() => {})
    };

    // 设置初始状态
    this.element.textContent = this.originalText;
  }

  /**
   * 开始动画
   */
  start(): void {
    if (this.isRunning) return;

    this.isRunning = true;
    this.startTime = performance.now() + this.options.delay;
    this.animate();
  }

  /**
   * 停止动画
   */
  stop(): void {
    this.isRunning = false;
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }
    this.element.textContent = this.originalText;
  }

  /**
   * 销毁实例
   */
  destroy(): void {
    this.stop();
  }

  /**
   * 动画循环
   */
  private animate = (): void => {
    if (!this.isRunning) return;

    const currentTime = performance.now();

    if (currentTime < this.startTime) {
      this.animationId = requestAnimationFrame(this.animate);
      return;
    }

    const elapsed = currentTime - this.startTime;
    const progress = Math.min(elapsed / this.options.duration, 1);

    if (progress < 1) {
      this.element.textContent = this.generateShuffledText(progress);
      this.animationId = requestAnimationFrame(this.animate);
    } else {
      this.element.textContent = this.originalText;
      this.isRunning = false;
      this.options.onComplete();
    }
  };

  /**
   * 生成混合文本 - Effect 1 (经典从左到右渐显)
   */
  private generateShuffledText(progress: number): string {
    const text = this.originalText;
    let result = '';

    // 计算当前应该显示到哪个字符
    const revealLength = Math.floor(progress * text.length);

    for (let i = 0; i < text.length; i++) {
      if (text[i] === ' ') {
        result += ' ';
      } else if (i < revealLength) {
        // 已经确定的字符，偶尔闪烁一下
        if (Math.random() > 0.95) {
          result += this.getRandomChar();
        } else {
          result += text[i];
        }
      } else {
        // 未确定的字符，显示随机字符
        result += this.getRandomChar();
      }
    }

    return result;
  }

  /**
   * 获取随机字符
   */
  private getRandomChar(): string {
    const originalText = this.originalText;

    // 如果原文本主要是数字，则使用数字字符集
    if (/^\$?[\d,]+\+?$/.test(originalText)) {
      const numberChars = '0123456789$,+';
      return numberChars[Math.floor(Math.random() * numberChars.length)];
    }

    // 否则使用完整字符集
    return this.chars[Math.floor(Math.random() * this.chars.length)];
  }

  /**
   * 设置新文本并重新开始动画
   */
  setText(newText: string): void {
    this.stop();
    this.originalText = newText;
    this.options.text = newText;
    this.start();
  }
}

/**
 * 创建 TypeShuffle 实例的工厂函数
 */
export function createTypeShuffle(element: HTMLElement, options?: TypeShuffleOptions): TypeShuffle {
  return new TypeShuffle(element, options);
}
