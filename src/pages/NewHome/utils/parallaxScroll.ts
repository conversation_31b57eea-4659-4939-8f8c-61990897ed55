// 精准的视差滚动 - 只影响背景元素，不破坏现有动画
interface ParallaxElement {
  element: HTMLElement;
  speed: number;
  originalTransform: string;
}

class ParallaxScroll {
  private elements: ParallaxElement[] = [];
  private isActive = true;

  constructor() {
    this.init();
  }

  private init() {
    // 延迟初始化，确保所有现有动画已完成
    setTimeout(() => {
      this.setupElements();
      this.bindEvents();
    }, 2000);
  }

  private setupElements() {
    // 非常精准的选择器，只影响背景和装饰元素
    const configs = [
      // 只影响背景图片，不影响内容
      { selector: '[class*="bgImage"]', speed: 0.5 },

      // Section级别的轻微视差
      { selector: 'section[class*="hero"]', speed: 0.95 },
      { selector: 'section[class*="features"]', speed: 0.9 },
      { selector: 'section[class*="ecosystem"]', speed: 0.85 },
      { selector: 'section[class*="community"]', speed: 0.8 }
    ];

    configs.forEach(config => {
      document.querySelectorAll(config.selector).forEach(el => {
        if (el instanceof HTMLElement) {
          // 避免重复处理已有动画的元素
          const hasScrollReveal = el.classList.toString().includes('reveal') || el.querySelector('[class*="reveal"]');

          if (!hasScrollReveal) {
            this.elements.push({
              element: el,
              speed: config.speed,
              originalTransform: el.style.transform || ''
            });
          }
        }
      });
    });

    console.log('Parallax initialized for', this.elements.length, 'elements');
  }

  private bindEvents() {
    let ticking = false;

    const updateParallax = () => {
      if (!this.isActive) return;

      const scrollY = window.scrollY;

      this.elements.forEach(({ element, speed, originalTransform }) => {
        const rect = element.getBoundingClientRect();

        // 在元素进入视口时才应用视差
        if (rect.top < window.innerHeight + 100 && rect.bottom > -100) {
          // 计算视差偏移 - 更温和的效果
          const offset = scrollY * (1 - speed) * 0.3;

          // 应用变换
          const newTransform = originalTransform
            ? `${originalTransform} translateY(${offset}px)`
            : `translateY(${offset}px)`;

          element.style.transform = newTransform;
        }
      });

      ticking = false;
    };

    const onScroll = () => {
      if (!ticking) {
        requestAnimationFrame(updateParallax);
        ticking = true;
      }
    };

    window.addEventListener('scroll', onScroll, { passive: true });
    updateParallax();
  }

  public toggle(active: boolean) {
    this.isActive = active;
    if (!active) {
      this.reset();
    }
  }

  private reset() {
    this.elements.forEach(({ element, originalTransform }) => {
      element.style.transform = originalTransform;
    });
  }
}

// 导出初始化函数
export function initParallaxScroll() {
  new ParallaxScroll();
}
