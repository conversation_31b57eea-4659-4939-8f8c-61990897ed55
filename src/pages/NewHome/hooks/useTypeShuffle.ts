import { useEffect, useRef } from 'react';
import { TypeShuffle, TypeShuffleOptions } from '../utils/TypeShuffle';

interface UseTypeShuffleOptions extends Omit<TypeShuffleOptions, 'text'> {
  text: string;
  autoStart?: boolean;
}

export const useTypeShuffle = (options: UseTypeShuffleOptions) => {
  const elementRef = useRef<HTMLSpanElement>(null);
  const instanceRef = useRef<TypeShuffle | null>(null);

  useEffect(() => {
    if (!elementRef.current) return;

    // 创建 TypeShuffle 实例
    instanceRef.current = new TypeShuffle(elementRef.current, {
      text: options.text,
      delay: options.delay || 0,
      duration: options.duration || 2000,
      iterations: options.iterations || 10,
      fps: options.fps || 30,
      onComplete: options.onComplete
    });

    // 如果 autoStart 为 true (默认)，则自动开始动画
    if (options.autoStart !== false) {
      instanceRef.current.start();
    }

    // 清理函数
    return () => {
      if (instanceRef.current) {
        instanceRef.current.destroy();
        instanceRef.current = null;
      }
    };
  }, [options.text, options.delay, options.duration, options.iterations, options.fps, options.autoStart]);

  // 提供控制方法
  const controls = {
    start: () => instanceRef.current?.start(),
    stop: () => instanceRef.current?.stop(),
    setText: (newText: string) => instanceRef.current?.setText(newText)
  };

  return [elementRef, controls] as const;
};
