import { observer } from 'mobx-react-lite';
import { useState, useEffect, useRef, useLayoutEffect, useMemo } from 'react';
import styles from './header.module.scss';
import Dropdown from '@/components/Dropdown/Dropdown';
import classNames from 'classnames';
import {
  CommunitySubMenu,
  ContractsSubMenu,
  DevelopersSubMenu,
  ProductsSubMenus,
  ResourcesSubMenu
} from '../utils/const';
import MobileMenu from '../MobileMenu/MobileMenu';
import { Lang, setLang, getLang } from '@/utils/select-lang';
import intl from 'react-intl-universal';
import { useIsMobile } from '@/hooks/useIsMobile';
import { useLocation } from 'react-router-dom';
import { ConnectButton } from '@/components/ConnectButton/ConnectButton';
import { NetworkErrorPopover, SwitchChain } from '@/components/SwitchChain/SwitchChain';
import { useWallet } from '@/components/WalletProvider/useWallet';
function isDescendant(parent: Node, child: Node): boolean {
  let node: Node | null = child;
  while (node) {
    if (node === parent) return true;
    node = node.parentNode as Node | null;
  }
  return false;
}
declare global {
  interface Window {
    gtag?: (...args: any[]) => void;
  }
}
const Header = observer(({ showConnectButton }: { showConnectButton?: boolean }) => {
  const [isLangOpen, setIsLangOpen] = useState(false);
  const [currentLang, setCurrentLang] = useState(() => getLang());
  const langSelectorRef = useRef<HTMLDivElement>(null);

  const languages = [
    { code: 'en-US', label: 'EN', displayName: 'English' },
    { code: 'zh-TC', label: '繁中', displayName: '繁体中文' },
    { code: 'zh-CN', label: '简中', displayName: '简体中文' }
  ];

  // 根据当前语言获取对应的链接
  const getLocalizedUrl = (baseUrl: string, lang: Lang) => {
    // 针对帮助中心链接的语言区分
    if (baseUrl.includes('support.tronlink.org')) {
      switch (lang) {
        case 'zh-CN':
          return 'https://support.tronlink.org/hc/zh-cn/articles/38903684778393-GasFree-%E9%92%B1%E5%8C%85%E4%BD%BF%E7%94%A8%E6%96%87%E6%A1%A3';
        case 'zh-TC':
          return 'https://support.tronlink.org/hc/zh-cn/articles/38903684778393-GasFree-%E9%92%B1%E5%8C%85%E4%BD%BF%E7%94%A8%E6%96%87%E6%A1%A3';
        case 'en-US':
        default:
          return 'https://support.tronlink.org/hc/en-us/articles/38903684778393-GasFree-User-Guide';
      }
    }
    // 其他链接保持原样
    return baseUrl;
  };

  const handleLangSelect = (lang: { code: string; label: string }) => {
    setCurrentLang(lang.code as Lang);
    setIsLangOpen(false);
    setLang(lang.code as Lang);
  };

  // 监听语言变化，确保组件状态同步
  useEffect(() => {
    const currentStoredLang = getLang();
    if (currentStoredLang !== currentLang) {
      setCurrentLang(currentStoredLang);
    }
  }, [currentLang]);

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (langSelectorRef.current && !isDescendant(langSelectorRef.current, event.target as Node)) {
        setIsLangOpen(false);
      }
    };

    document.body.addEventListener('click', handleClickOutside, { capture: true });
    return () => {
      document.body.removeEventListener('click', handleClickOutside, { capture: true });
    };
  }, []);

  const [mMenuOpen, setMMenuOpen] = useState(false);
  function handleOpenMobileMenu() {
    setMMenuOpen(true);
  }
  function handleCloseMobileMenu() {
    setMMenuOpen(false);
  }

  const headerRef = useRef<null | HTMLElement>(null);
  const [showBg, setShowBg] = useState<boolean>(false);
  const isMobile = useIsMobile();
  const onScroll = function () {
    if (!headerRef.current?.parentElement || isMobile) {
      return;
    }
    if (headerRef.current.parentElement.scrollTop > 10) {
      setShowBg(true);
    } else {
      setShowBg(false);
    }
  };
  useLayoutEffect(() => {
    const node = headerRef.current;
    node?.parentElement?.addEventListener('scroll', onScroll);
    return () => {
      node?.parentElement?.removeEventListener('scroll', onScroll);
    };
  }, [headerRef]);

  const { isNetworkMatch } = useWallet();
  const { pathname, search } = useLocation();
  const currentRoute = useMemo(() => pathname.slice(1) || 'home', [pathname]);
  const env = import.meta.env.VITE_APP_ENV || '';
  const shouldShowConnectButton = useMemo(() => {
    if (env !== 'nile') return true;
    const searchParams = new URLSearchParams(search);
    return searchParams.has('chain');
  }, [env, search]);

  return (
    <header className={classNames(styles.header, showBg ? styles.floated : '')} ref={headerRef} onScroll={onScroll}>
      <div className={styles.container}>
        <div className={styles.navWrap}>
          {/* Logo */}
          <div className={classNames(styles.logo, showConnectButton ? styles.hide : '')}>
            <a
              href="/"
              onClick={() =>
                window.gtag &&
                window.gtag('event', pathname.startsWith('/3rdparty') ? '3rdparty_top_left_icon' : 'home_top_left_icon')
              }
            >
              <div className={styles.logoImage}></div>
            </a>
          </div>

          {/* Navigation Menu */}
          <nav className={styles.nav}>
            <Dropdown
              items={[
                ...ProductsSubMenus.slice(0, 1),
                {
                  label: (
                    <div className="flex items-center">
                      <span className={styles.addIcon}>+</span>
                      <span>{ProductsSubMenus[1].label}</span>
                    </div>
                  ),
                  href: ProductsSubMenus[1].href,
                  target: ProductsSubMenus[1].target,
                  event: ProductsSubMenus[1].event
                },
                ...ProductsSubMenus.slice(2)
              ].map(item => (
                <a
                  key={item.href}
                  href={item.href}
                  target={item.target || '_self'}
                  className={styles.item}
                  onClick={() =>
                    item.event &&
                    window.gtag &&
                    window.gtag(
                      'event',
                      pathname.startsWith('/3rdparty')
                        ? item.event.replace('home_header_', '3rdparty_header_')
                        : item.event
                    )
                  }
                >
                  {item.label}
                </a>
              ))}
              style={{ minWidth: 200 }}
            >
              <div className={`${styles.navItem} `}>{intl.get('newhome_nav_products')}</div>
            </Dropdown>
            <Dropdown
              items={DevelopersSubMenu.map(item => (
                <a
                  key={item.href}
                  href={item.href}
                  target={item.target || '_self'}
                  className={styles.item}
                  onClick={() => item.event && window.gtag && window.gtag('event', item.event)}
                >
                  {item.label}
                </a>
              ))}
            >
              <div className={`${styles.navItem} `}>{intl.get('newhome_nav_developers')}</div>
            </Dropdown>
            <Dropdown
              items={ResourcesSubMenu.map(item => {
                // 判断是否为帮助中心中文链接
                const isHelpCn =
                  typeof item.href === 'string' &&
                  item.href.includes('support.tronlink.org') &&
                  currentLang === 'zh-CN';
                return (
                  <a
                    key={item.href}
                    target={item.target || '_self'}
                    href={getLocalizedUrl(item.href, currentLang)}
                    className={styles.item}
                    onClick={e => {
                      if (item.event)
                        window.gtag &&
                          window.gtag(
                            'event',
                            pathname.startsWith('/3rdparty')
                              ? item.event.replace('home_header_', '3rdparty_header_')
                              : item.event
                          );
                      if (isHelpCn)
                        window.gtag &&
                          window.gtag(
                            'event',
                            pathname.startsWith('/3rdparty') ? '3rdparty_header_help_cn' : 'home_header_help_cn'
                          );
                    }}
                  >
                    {item.label}
                  </a>
                );
              })}
              style={{ minWidth: 200 }}
            >
              <div className={`${styles.navItem} `}>{intl.get('newhome_nav_resource')}</div>
            </Dropdown>
            <Dropdown
              items={ContractsSubMenu.map(item => (
                <a
                  key={item.href}
                  target={item.target || '_self'}
                  href={item.href}
                  className={styles.item}
                  onClick={() => item.event && window.gtag && window.gtag('event', item.event)}
                >
                  {item.label}
                </a>
              ))}
            >
              <div className={`${styles.navItem} `}>{intl.get('newhome_nav_contact')}</div>
            </Dropdown>
            <Dropdown
              items={CommunitySubMenu.map(item => (
                <a
                  key={item.href}
                  target={item.target || '_self'}
                  href={item.href}
                  className={styles.item}
                  onClick={() => item.event && window.gtag && window.gtag('event', item.event)}
                >
                  {item.label}
                </a>
              ))}
            >
              <div className={`${styles.navItem} `}>{intl.get('newhome_nav_community')}</div>
            </Dropdown>
          </nav>
        </div>
        <div className={styles.buttonsWrap}>
          {showConnectButton && (
            <>
              {env === 'test' && !isMobile ? (
                <NetworkErrorPopover isError={!isNetworkMatch}>
                  <SwitchChain isError={!isNetworkMatch} style={{ marginRight: '10px' }} />{' '}
                </NetworkErrorPopover>
              ) : null}
              {!isMobile && currentRoute.includes('transfer') ? <span></span> : null}
              {shouldShowConnectButton && <ConnectButton classNames={styles.connectButton} />}
            </>
          )}
          {/* Language Selector */}
          <div className={styles.langSelector} ref={langSelectorRef}>
            <div
              className={styles.langButton}
              onMouseEnter={e => {
                e.stopPropagation();
                setIsLangOpen(!isLangOpen);
              }}
              onMouseLeave={e => {
                e.stopPropagation();
                setIsLangOpen(false);
              }}
            >
              <div className={styles.globeIcon}></div>
              <span className={styles.langText}>
                {languages.find(({ code }) => code === currentLang)?.label || 'EN'}
              </span>
            </div>

            <div className={classNames(styles.langDropdown, { [styles.open]: isLangOpen })}>
              {languages.map(lang => (
                <div
                  key={lang.code}
                  className={classNames(styles.langOption, currentLang === lang.code ? styles.active : '')}
                  onClick={e => {
                    e.stopPropagation();
                    handleLangSelect(lang);
                    window.gtag &&
                      window.gtag(
                        'event',
                        pathname.startsWith('/3rdparty')
                          ? `3rdparty_header_${lang.code.replace('-', '').toLowerCase()}`
                          : `home_header_${lang.code.replace('-', '').toLowerCase()}`
                      );
                  }}
                  onMouseEnter={e => {
                    e.stopPropagation();
                    setIsLangOpen(true);
                  }}
                  onMouseLeave={e => {
                    e.stopPropagation();
                    setIsLangOpen(false);
                  }}
                >
                  <span>{lang.displayName}</span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/** Mobile Menu */}
        <div className={styles.mobileMenuIcon} onClick={handleOpenMobileMenu}></div>
        <MobileMenu
          open={mMenuOpen}
          onClose={handleCloseMobileMenu}
          onLangChange={handleLangSelect}
          currentLang={currentLang}
          showSwitchChain={!!showConnectButton}
        />
      </div>
    </header>
  );
});

export { Header };
