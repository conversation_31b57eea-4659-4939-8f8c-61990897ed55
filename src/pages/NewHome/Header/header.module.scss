.header {
  // width: 100%;
  height: 79px;
  position: sticky;
  top: 20px;
  margin: 0 45px;
  z-index: 1000;
  transition: background 0.3s ease-in;

  background: transparent;
  backdrop-filter: blur(40px);
  border-radius: 20px;
  flex: 1 0 79px;

  &.floated {
    background: rgba(255, 255, 255, 0.15);
  }
}

.container {
  position: relative;
  max-width: 1400px;
  margin: 0 auto;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
}

.logo {
  display: flex;
  align-items: center;
  cursor: pointer;

  .logoImage {
    width: 124px;
    height: 40px;
    background: url(../images/logo.svg) no-repeat center/contain;
    transition: opacity 0.2s ease;
  }
}
.mobileMenuIcon {
  display: none;
}

.addIcon {
  background-color: #33ffa9;
  border-radius: 4px;
  width: 16px;
  height: 16px;
  display: inline-block;
  margin-right: 6px;
  color: rgb(55, 55, 55);
  line-height: 16px;
  font-weight: 700;
  text-align: center;
}
.navWrap {
  display: flex;
}
.nav {
  display: flex;
  align-items: center;
  gap: 55px;
  margin-left: 60px;

  :global(.active) .navItem {
    color: #00ff88;
  }

  .navItem {
    font-family: 'Sora', sans-serif;
    font-weight: 600;
    font-size: 14px;
    line-height: 100%;
    letter-spacing: 0%;
    text-transform: capitalize;
    color: #ffffff;
    text-decoration: none;
    padding: 8px 0;
    position: relative;
    transition: all 0.2s ease;

    &:hover {
      color: #00ff88;
    }
  }
  .item {
    display: block;
    padding: 0px 10px;
    color: #fff;
    text-align: left;
    font-size: 14px;
    line-height: 30px;
    text-decoration: none;
    border-radius: 6px;
    white-space: pre;
    word-wrap: none;
    transition:
      background 0.2s,
      color 0.2s;
    border: none;
    background: none;
    cursor: pointer;

    & + .item {
      margin-top: 10px;
    }
  }

  .item:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #33ffa9;
  }
}

.buttonsWrap {
  display: flex;
  flex-direction: row;
  .connectButton {
    margin-right: 10px;
  }
}

.langSelector {
  position: relative;
  z-index: 200;

  .langButton {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 3px;
    padding: 8px 10px 8px 10px;
    background: rgba(39, 39, 43, 1);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 82px;
    background-color: rgba(255, 255, 255, 0.1);

    .globeIcon {
      width: 16px;
      height: 16px;
      background: url('../images/global.svg') no-repeat center/contain;
      display: block;
    }

    .langText {
      font-family: 'Sora', sans-serif;
      font-size: 14px;
      font-weight: 600;
      color: #ffffff;
      letter-spacing: 0%;
    }

    &:hover {
      background: rgba(255, 255, 255, 0.25);
    }
  }

  .langDropdown {
    position: absolute;
    top: calc(100% + 8px); // 显示在按钮下方
    right: 0;
    left: 0;
    min-width: 82px;
    margin: auto;
    display: flex;
    flex-direction: column;
    padding: 5px;
    gap: 8px;
    z-index: 1000;
    background-color: #212121;
    transform: translateX(0%) translateY(-10px);
    opacity: 0;
    border-radius: 10px;
    transition:
      opacity 0.3s,
      transform 0.3s;
    border: 1px solid rgba(255, 255, 255, 0.2);

    .langOption {
      display: flex;
      align-items: center;
      gap: 3px;
      padding: 8px 0;
      justify-content: center;
      padding-left: 0px;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s ease;
      min-width: 60px;
      white-space: nowrap;

      .globeIcon {
        width: 16px;
        height: 16px;
        background: url('../images/global.svg') no-repeat center/contain;
        display: block;
      }

      span {
        font-family: 'Sora', sans-serif;
        font-size: 14px;
        font-weight: 500;
        color: #ffffff;
        letter-spacing: 0%;
      }

      &:hover {
        background: rgba(255, 255, 255, 0.1);
      }
    }
    .active {
      background: rgba(255, 255, 255, 0.1);
    }
  }
  .open {
    opacity: 1;
    transform: translateX(0%) translateY(0);
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .container {
    padding: 0 16px;
  }

  .nav {
    gap: 24px;
  }
}

@media (max-width: 768px) {
  .header {
    top: -1px;
    margin: 0;
    height: 60px;
    padding: 15px 0 15px;
    flex: 1 0 60px;
    .container {
      justify-content: space-between;
    }
    &.floated {
      background-color: black;
      border-radius: 0;
      top: -1px;
      backdrop-filter: none;
    }
  }
  .nav {
    display: none;
  }

  .connectButton {
    align-self: center;
    margin-left: 0;
  }

  .langSelector {
    display: none;
  }

  .logo .logoImage {
    background: url(../images/m/logo.svg) no-repeat center/auto;
    width: 84px;
  }
  .logo.hide {
    display: none;
  }
  .mobileMenuIcon {
    display: block;
    width: 24px;
    height: 24px;
    background: url(../images/m/menu.png) no-repeat center/contain;
    position: absolute;
    right: 15px;
  }

  // 移动端可以考虑添加汉堡菜单
  .mobileMenu {
    display: block;
    width: 24px;
    height: 24px;
    cursor: pointer;
  }
}
