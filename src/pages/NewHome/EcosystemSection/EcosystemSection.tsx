import React, { useState } from 'react';
import styles from './ecosystemSection.module.scss';
// import ecosystemBg from '../images/EcosystemBg.png';

// Import partner logos
import tronlinkLogo from '../images/partners/tronlink.png';
import imtokenLogo from '../images/partners/imtoken.png';
import guardaLogo from '../images/partners/guarda.png';
import kleverLogo from '../images/partners/klever.png';
import edirLogo from '../images/partners/edir.png';
import winklinkLogo from '../images/partners/winklink.png';
import sunLogo from '../images/partners/sun.png';
import bittorrentLogo from '../images/partners/bttc.png';
import apenftLogo from '../images/partners/apenft.png';
import justlendLogo from '../images/partners/justlend.png';
import { ScrollReveal } from '../components/ScrollReveal/ScrollReveal';
import intl from 'react-intl-universal';

const partners = [
  { name: 'TronLink', link: 'https://www.tronlink.org/', logo: tronlinkLogo, event: 'tronlink' },
  { name: 'imToken', link: 'https://token.im/', logo: imtokenLogo, event: 'imtoken' },
  { name: 'Guarda', link: 'https://guarda.com/', logo: guardaLogo, event: 'guarda' },
  { name: 'Klever', link: 'https://klever.io/', logo: kleverLogo, event: 'klever' },
  { name: 'eDir', link: 'https://edir.so/', logo: edirLogo, event: 'edir' },
  { name: 'JustLend', link: 'https://justlend.org/', logo: justlendLogo, event: 'justlend' },
  { name: 'WinkLink', link: 'https://winklink.org/', logo: winklinkLogo, event: 'winklink' },
  { name: 'SUN.io', link: 'https://sun.io/', logo: sunLogo, event: 'sun' },
  { name: 'BitTorrent', link: 'https://www.bittorrent.com/', logo: bittorrentLogo, event: 'bt' },
  { name: 'APENFT', link: 'https://apenft.io/', logo: apenftLogo, event: 'apenft' }
];

const EcosystemSection: React.FC = () => {
  const [active1, setActive1] = useState(false);
  const [active2, setActive2] = useState(false);
  function handleMouseEnter1() {
    setActive1(true);
  }
  function handleMouseOut1() {
    setActive1(false);
  }
  function handleMouseEnter2() {
    setActive2(true);
  }
  function handleMouseOut2() {
    setActive2(false);
  }
  return (
    <section className={styles.ecosystemSection}>
      <div className={styles.container}>
        <div className={styles.header}>
          <div>
            <ScrollReveal delay={0} duration={0.8} distance="60px" direction="up">
              <h2 className={styles.title}>{intl.getHTML('newhome_ecosys_title')}</h2>
            </ScrollReveal>
            <ScrollReveal delay={400} duration={0.8} distance="60px" direction="up">
              <p className={styles.description}>{intl.get('newhome_ecosys_desc')}</p>
            </ScrollReveal>
          </div>
          <ScrollReveal delay={200} duration={0.8} distance="60px" direction="up">
            <div className={styles.stats}>
              <div className={styles.statItem}>
                <span className={styles.statValue}>6M+</span>
                <span className={styles.statLabel}>{intl.get('newhome_ecosys_user_base')}</span>
              </div>
            </div>
          </ScrollReveal>
        </div>
        <div className={styles.partnersGridWrap}>
          <div className={[styles.mask, styles.leftMask].join(' ')}></div>
          <div className={[styles.mask, styles.rightMask].join(' ')}></div>
          <div className={`${styles.partnersGrid} ${styles.scrollLeft} ${active1 ? styles.paused : ''}`}>
            {[...partners.slice(0, 5), ...partners.slice(0, 5)].map((partner, index) => (
              <a
                key={index}
                href={partner.link}
                target="_blank"
                rel="noopener noreferrer"
                className={styles.partnerCard}
                onMouseEnter={handleMouseEnter1}
                onMouseLeave={handleMouseOut1}
                onClick={() => window.gtag && window.gtag('event', `home_body_${partner.event}`)}
              >
                <img src={partner.logo} alt={partner.name} />
                <span className={styles.partnerName}>{partner.name}</span>
              </a>
            ))}
          </div>
          <div className={`${styles.partnersGrid} ${styles.scrollRight} ${active2 ? styles.paused : ''}`}>
            {[...partners.slice(5), ...partners.slice(5)].map((partner, index) => (
              <a
                key={index}
                href={partner.link}
                target="_blank"
                rel="noopener noreferrer"
                className={styles.partnerCard}
                onMouseEnter={handleMouseEnter2}
                onMouseLeave={handleMouseOut2}
                onClick={() => window.gtag && window.gtag('event', `home_body_${partner.event}`)}
              >
                <img src={partner.logo} alt={partner.name} />
                <span className={styles.partnerName}>{partner.name}</span>
              </a>
            ))}
          </div>
          <div className={styles.partnersGridMobile}>
            {partners.map((partner, index) => (
              <a
                key={index}
                href={partner.link}
                target="_blank"
                rel="noopener noreferrer"
                className={styles.partnerCard}
                onClick={() => window.gtag && window.gtag('event', `home_body_${partner.event}`)}
              >
                <img src={partner.logo} alt={partner.name} />
                <span className={styles.partnerName}>{partner.name}</span>
              </a>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default EcosystemSection;
