.ecosystemSection {
  // width: 1200px;
  margin: 0 auto;
  padding: 280px 0px 128px;
  background: url('../images/bg-ecosystem.png') no-repeat center top/ auto;
  position: relative;
  z-index: 21;
  margin-top: -100px;

  .container {
    position: relative;
    z-index: 1;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0px 0px 0px;
    text-align: center;
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }

  .title,
  .title span {
    font-family: 'Sora';
    font-size: 40px;
    font-weight: 700;
    line-height: 50px;
    color: #ffffff;
    margin: 0;
    text-align: left;
    br {
      display: none;
    }
  }

  .stats {
    .statItem {
      display: flex;
      flex-direction: column;
      align-items: flex-end;

      .statValue {
        font-family: 'Sora';
        font-size: 32px;
        font-weight: 700;
        color: #00ff88;
        line-height: 100%;
      }

      .statLabel {
        font-family: 'Sora';
        font-size: 14px;
        font-weight: 600;
        color: rgba(255, 255, 255, 1);
        line-height: 150%;
      }
    }
  }

  .description {
    font-family: 'Sora';
    font-size: 14px;
    font-weight: 400;
    line-height: 18px;
    color: rgba(255, 255, 255, 0.6);
    margin: 20px 0 0px;
    text-align: left;
  }

  .partnersGridWrap {
    width: 100%;
    position: relative;
    overflow: hidden;
    padding-top: 84px;
    .mask {
      position: absolute;
      top: 84px;
      bottom: 0;
      margin: auto;
      z-index: 2;
      width: 300px;
    }
    .leftMask {
      left: -120px;
      background: linear-gradient(90deg, #020203 65.42%, rgba(22, 22, 39, 0) 100%);
    }
    .rightMask {
      background: linear-gradient(270deg, #020203 65.42%, rgba(22, 22, 39, 0) 100%);
      right: -120px;
    }
  }
  .partnersGridMobile {
    display: none;
  }
  .partnersGrid {
    display: grid;
    grid-template-columns: repeat(10, 1fr);
    gap: 20px;

    width: 100%;

    &.paused {
      animation-play-state: paused;
    }

    .partnerCard {
      background: rgba(255, 255, 255, 0.05);
      border-radius: 16px;
      padding: 24px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 6px;
      transition: all 0.3s ease;
      text-decoration: none;
      width: 180px;
      height: 180px;

      &:hover {
        transform: translateY(-4px);
        background: rgba(255, 255, 255, 0.1);
      }

      img {
        width: 60px;
        height: 60px;
        object-fit: contain;
      }

      .partnerName {
        font-family: 'Sora';
        font-size: 18px;
        font-weight: 600;
        color: #ffffff;
      }
    }
  }
  .partnersGrid + .partnersGrid {
    margin-top: 20px;
  }
}

@keyframes scrollToLeft {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(-1000px);
  }
}
@keyframes scrollToRight {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(1000px);
  }
}
.scrollLeft {
  animation-name: scrollToLeft;
  animation-duration: 15s;
  animation-timing-function: linear;
  animation-iteration-count: infinite;
}
.scrollRight {
  justify-content: end;
  animation-name: scrollToRight;
  animation-duration: 15s;
  animation-timing-function: linear;
  animation-iteration-count: infinite;
}

@media screen and (max-width: 798px) {
  .ecosystemSection {
    padding: 60px 0 100px;
    width: unset;
    background: url('../images/m/bg-ecosystem.png') no-repeat center top/auto;
    background-size: 375px 375px;
    .container {
      padding: 0 20px;
    }
    .header {
      margin-bottom: 10px;
      .title,
      .title span {
        font-size: 24px;
        font-weight: 800;
        line-height: 1.2;
        br {
          display: inline;
        }
      }

      .statItem {
        .statValue {
          font-size: 20px;
          font-weight: 800;
          margin-bottom: 5px;
        }
        .statLabel {
          font-size: 12px;
          font-weight: 500;
        }
      }
    }
    .description {
      font-size: 11px;
    }
    .partnersGrid {
      display: none;
    }
    .mask {
      display: none;
    }
    .partnersGridMobile {
      display: grid;
      grid-template-columns: repeat(2, 1fr); /* 两列，等宽 */
      grid-template-rows: repeat(5, auto); /* 十行，高度自动 */
      gap: 20px;
      justify-items: center;
      place-content: center;
      justify-content: center;
      .partnerCard {
        width: 120px;
        height: 120px;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        border-radius: 14px;
        background-color: rgba(255, 255, 255, 0.1);
        &:nth-child(2n + 1) {
          place-self: end;
        }
        &:nth-child(2n) {
          place-self: start;
        }
        img {
          width: 40px;
          height: 40px;
          margin-bottom: 8px;
        }
        .partnerName {
          font-size: 12px;
          font-weight: 600;
          color: white;
        }
      }
    }
  }
}
