import React from 'react';
import styles from './communityBottomSection.module.scss';
import Footer from '../Footer/Footer';
import { ScrollReveal } from '../components/ScrollReveal/ScrollReveal';
import intl from 'react-intl-universal';

const CommunityBottomSection: React.FC = () => (
  <div className={styles.communityBottomSection}>
    <div className={styles.sectionBottom}>
      <ScrollReveal delay={0} duration={0.8} distance="60px" direction="up">
        <h2 className={styles.title}>{intl.get('newhome_future_title')}</h2>
      </ScrollReveal>
      <ScrollReveal delay={200} duration={0.8} distance="60px" direction="up">
        <p className={styles.description}>{intl.get('newhome_future_desc')}</p>
      </ScrollReveal>
      <ScrollReveal delay={400} duration={0.8} distance="60px" direction="up">
        <a
          href="https://docs.google.com/forms/d/e/1FAIpQLSfrj1A0mHKFtxfPQ5VY-kC56E7ZXVl6KBN6bB02uzzPsDIjPg/viewform?usp=header"
          target="_blank"
          rel="noopener noreferrer"
          className={styles.ctaButton}
        >
          <span className={styles.buttonText}>{intl.get('newhome_future_touch')}</span>
        </a>
      </ScrollReveal>
    </div>
    <Footer />
  </div>
);

export default CommunityBottomSection;
