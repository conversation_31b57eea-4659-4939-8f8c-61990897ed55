.communityBottomSection {
  width: 100%;
  padding: 0;
  position: relative;
  z-index: 1;
  min-height: auto;
  display: flex;
  flex-direction: column;
  background-color: #000000;
  background-image: url('../images/bg-communityBottom.jpg');
  background-size: auto auto;
  background-position: top center;
  background-repeat: no-repeat;
}

.sectionBottom {
  max-width: 1200px;
  margin: 0px auto 0;
  text-align: center;
  padding: 30px 20px 0px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
  z-index: 2;
}

.title {
  font-size: 40px;
  font-weight: 800;
  color: #ffffff;
  margin-bottom: 20px;
  line-height: 42px;
  font-family: 'Sora', sans-serif;
}

.description {
  font-family: 'Sora', sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 21px;
  letter-spacing: 0%;
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 34px;
  margin-left: auto;
  margin-right: auto;
}

.ctaButton {
  display: inline-block;
  padding: 0px 20px;
  background: rgba(51, 255, 169, 1);
  border: none;
  border-radius: 8px;
  font-family: 'Sora', sans-serif;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  white-space: nowrap;
  text-align: center;
  color: #000;
  line-height: 40px;
  text-decoration: none;
  margin-bottom: 142px;
  position: relative;
  overflow: hidden;
  width: auto;
  height: 40px;

  // Calypso 按钮效果实现
  .buttonText {
    display: block;
    position: relative;
    z-index: 10;
    transition: color 0.4s ease;
    opacity: 1;
    letter-spacing: 0.5px;
    color: #000;
    font-weight: 600;
  }

  &::before {
    content: '';
    position: absolute;
    background: #fff;
    width: 120%;
    height: 0;
    padding-bottom: 120%;
    top: -110%;
    left: -10%;
    border-radius: 50%;
    transform: translate3d(0, 68%, 0) scale3d(0, 0, 0);
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #fff;
    transform: translate3d(0, -100%, 0);
    transition: transform 0.4s cubic-bezier(0.1, 0, 0.3, 1);
  }

  &:focus {
    outline: none;
  }

  &:hover {
    &::before {
      transform: translate3d(0, 0, 0) scale3d(1, 1, 1);
      transition: transform 0.4s cubic-bezier(0.1, 0, 0.3, 1);
    }

    &::after {
      transform: translate3d(0, 0, 0);
      transition-duration: 0.05s;
      transition-delay: 0.4s;
      transition-timing-function: linear;
    }

    .buttonText {
      color: #000;
      animation:
        moveScaleUpInitial 0.3s forwards,
        moveScaleUpEnd 0.3s forwards 0.3s;
    }
  }
}

// Calypso 按钮文字动画
@keyframes moveScaleUpInitial {
  to {
    transform: translate3d(0, -100%, 0) scale3d(1, 1.5, 1);
    opacity: 0;
  }
}

@keyframes moveScaleUpEnd {
  from {
    transform: translate3d(0, 100%, 0) scale3d(1, 1.5, 1);
    opacity: 0;
  }
  to {
    transform: translate3d(0, 0, 0) scale3d(1, 1, 1);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .communityBottomSection {
    min-height: 100vh;
    background-size: 100% auto;
    background-position: center 50px;
    display: flex;
    flex-direction: column;
    align-items: center;
    background-image: url(../images/m/bg-communityBottom.png);
  }

  .sectionBottom {
    height: auto;
    min-height: 60vh;
    padding: 40px 20px;
    max-width: 100%;
  }

  .title {
    font-size: 28px;
    margin-bottom: 16px;
    line-height: 1.3;
  }

  .description {
    font-size: 14px;
    line-height: 140%;
    margin-bottom: 32px;
    padding: 0 10px;
  }

  .ctaButton {
    padding: 0px 24px;
    font-size: 14px;
    margin-top: 24px;
    border-radius: 8px;
    line-height: 30px;
  }
  .ctaButton:focus {
    opacity: 0.8;
  }
}

@media (max-width: 480px) {
  .communityBottomSection {
    // background-size: 120% auto;
    // background-position: center 30px;
    align-items: center;
  }

  .sectionBottom {
    padding: 30px 15px;
    min-height: 50vh;
  }

  .title {
    font-size: 24px;
    margin-bottom: 12px;
    line-height: 1.2;
  }

  .description {
    font-size: 13px;
    line-height: 130%;
    margin-bottom: 24px;
  }

  .ctaButton {
    padding: 0px 20px;
    font-size: 13px;
    margin-top: 20px;
    line-height: 40px;
  }
}
