.communitySection {
  width: 100%;
  margin: 0 0;
  padding: 80px 20px 154px 20px;
  overflow: hidden;
  position: relative;
  background: url('../images/bg-community.jpg') no-repeat center top/auto;
  background-color: #000000;

  &.scrolled {
    .usersGridWrap {
      transform: translateX(-572px); // 512 +
    }
    .buttonL {
      opacity: 1;
      cursor: pointer;
    }
    .buttonR {
      opacity: 0;
      cursor: default;
    }
  }
  .mask {
    position: absolute;
    top: 0;

    width: 1293px;
    height: 100%;
    background: url(../images/mask-community.png) no-repeat center 0 / auto;
    z-index: 20;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .button {
    position: absolute;
    left: 40px;
    top: 443px;
    width: 48px;
    height: 48px;
    background: url(../images/arrow-r.svg) no-repeat center/contain;
    transition: opacity 0.3s ease-in;
    cursor: pointer;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: url(../images/arrow-r-hover.svg) no-repeat center/contain;
      opacity: 0;
      transition: opacity 0.3s ease-in-out;
    }

    &:hover {
      &::before {
        opacity: 1;
      }
    }

    &::after {
      content: '';
      display: block;
      width: 0;
      height: 0;
      background-image: url(../images/arrow-r-hover.svg);
    }
  }
  .buttonL {
    right: 40px;
    left: unset;
    transform: rotate(180deg);
  }
  .mask1 {
    right: 50%;
    left: unset;
    transform: translateX(-620px); // 600 - 67的蒙版
    background: url(../images/mask-community-l.png) no-repeat center/auto;
  }
  .mask2 {
    // display: none;
    width: 1426px;
    right: unset;
    left: 50%;
    transform: translateX(620px); // 600 - 67的蒙版
  }
  .container {
    text-align: center;
    max-width: 1440px;
    margin: 0 auto;
    overflow: hidden;
  }

  .title {
    font-family: 'Sora';
    font-size: 40px;
    font-weight: 700;
    line-height: 50px;
    color: #ffffff;
    margin: 0 0 20px;
  }

  .description {
    font-family: 'Sora';
    font-size: 16px;
    font-weight: 400;
    line-height: 18px;
    color: rgba(255, 255, 255, 0.7);
    margin: 0 auto 80px;
    max-width: 600px;
  }

  .scrollContainer {
    width: 100%;
    overflow: hidden;
    position: relative;
  }
  .containerPC {
    display: block;
  }
  .containerM {
    display: none;
  }
  .buttonL {
    opacity: 0;
    cursor: default;
  }
  .buttonR {
    opacity: 1;
    cursor: pointer;
  }
  .usersGridWrap {
    display: flex;
    flex-wrap: nowrap;
    width: 1200px;
    margin: auto;
    transition: transform 0.3s ease-in;
    padding: 0 20px;
  }
  .usersGrid {
    flex: 0 0 auto;
    display: grid;
    grid-template-columns: 438px 438px 438px 438px;
    grid-template-rows: 209px 209px;
    grid-template-areas:
      'main top-1 main2 top-2'
      'main bottom-1 main2 bottom-2';
    gap: 20px;
    // margin-bottom: 64px;
    // height: 640px;

    .userCard {
      position: relative;
      border-radius: 23px;
      overflow: hidden;
      cursor: pointer;
      height: 100%;

      // 左侧大卡片 Freja
      &:nth-child(1) {
        grid-area: main;
      }

      // 右上第一个 Ayaka
      &:nth-child(2) {
        grid-area: top-1;
      }

      // 右上第二个 Julien
      &:nth-child(3) {
        grid-area: top-2;
      }
      // 第三列
      &:nth-child(4) {
        grid-area: main2;
      }
      // 右下第一个 Muhammad
      &:nth-child(5) {
        grid-area: bottom-1;
      }

      // 右下第二个 Grazhio
      &:nth-child(6) {
        grid-area: bottom-2;
        .userInfo {
          .userQuote {
            padding-right: 30px;
          }
        }
      }

      // 统一右侧卡片大小
      &:not(:first-child) {
        // aspect-ratio: 1;
      }

      &::before {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        height: 75%;
        background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.9) 100%);
        opacity: 0.6;
        transition: opacity 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        z-index: 1;
      }

      // 添加全屏蒙版
      &::after {
        content: '';
        position: absolute;
        inset: 0;
        background: rgba(0, 0, 0, 0.4);
        opacity: 0;
        transition: opacity 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      }

      .userImage {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      }

      .userInfo {
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        padding: 24px 24px 32px 24px;
        transform: translateY(0);
        transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        z-index: 2;

        .userName {
          font-family: 'Sora';
          font-size: 20px;
          font-weight: 600;
          color: #33ffa9;
          margin: 0 0 12px;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
          transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
          text-align: left;
          opacity: 1;
          transform: translateY(0);
        }

        .userQuote {
          font-family: 'Sora';
          font-size: 14px;
          font-weight: 400;
          color: rgba(255, 255, 255, 0.9);
          margin: 8px 0 0 0;
          opacity: 1;
          transform: translateY(0);
          transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
          line-height: 150%;
          text-align: left;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
          max-height: 21px;
        }
      }

      &:hover {
        &::before {
          opacity: 1;
        }

        &::after {
          opacity: 1;
        }

        .userImage {
          transform: scale(1.05);
        }

        .userInfo {
          transform: translateY(0);

          .userName {
            text-shadow: 0 2px 8px rgba(51, 255, 169, 0.3);
          }

          .userQuote {
            -webkit-line-clamp: unset;
            overflow: visible;
            display: block;
            max-height: none;
            opacity: 0.95;
          }
        }
      }
    }
  }
  .usersGrid + .usersGrid {
    margin-left: 20px;
  }
}

@media screen and (max-width: 798px) {
  .communitySection {
    padding: 30px 20px 100px 20px;
    .title {
      font-size: 24px;
      font-weight: 800;
      margin-bottom: 10px;
      line-height: 1.2;
    }
    .description {
      font-size: 11px;
      font-weight: 400;
      margin-bottom: 20px;
    }
    .usersGrid {
      grid-template-columns: 220px 220px 220px 220px;
      grid-template-rows: 104px 104px;
      gap: 10px;
      transition: transform 0.3s linear;
      & + .usersGrid {
        margin-left: 10px;
      }
      .userCard {
        .userInfo {
          padding: 15px;
          .userName {
            font-size: 12px;
            font-weight: 800;
          }
          .userQuote {
            font-weight: 400;
            font-size: 10px;
          }
        }
      }
    }
    .usersGridWrap {
      min-width: none;
      width: unset;
      border-radius: 23px;
      overflow: hidden;
    }
    .containerPC {
      display: none;
    }
    .containerM {
      display: block;
      .usersGridWrap {
        padding: 0;
      }
    }
    .buttonWrap {
      margin-top: 20px;
      width: 100%;
      display: flex;
      justify-content: flex-end;
      .button {
        position: relative;
        top: 0;
        right: unset;
        left: unset;
        width: 24px;
        height: 24px;
        background: url(../images/arrow-r.svg) no-repeat center/contain;
        transition: opacity 0.3s ease-in;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: url(../images/arrow-r-hover.svg) no-repeat center/contain;
          opacity: 0;
          transition: opacity 0.3s ease-in-out;
        }

        &.disabled {
          &::before {
            opacity: 1;
          }
        }

        &:hover:not(.disabled) {
          &::before {
            opacity: 1;
          }
        }

        &::after {
          content: '';
          display: block;
          width: 0;
          height: 0;
          background-image: url(../images/arrow-r-hover.svg);
        }
      }
      .buttonL {
        opacity: 1;
        margin-right: 10px;
        transform: rotate(180deg);
      }
    }
    .scrollLeft {
      animation-name: scrollToLeft_M;
      animation-duration: 15s;
      animation-timing-function: linear;
      animation-iteration-count: infinite;
    }
  }
}
