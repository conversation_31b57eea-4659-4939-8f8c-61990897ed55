import React, { useLayoutEffect, useMemo, useRef, useState } from 'react';
import styles from './communitySection.module.scss';

// Import user images
import frejaImage from '../images/community/freja.jpg';
import ayakaImage from '../images/community/ayaka.jpg';
import julienImage from '../images/community/julien.jpg';
import llyaImage from '../images/community/llya.jpg';
import muhammadImage from '../images/community/muhammad.jpg';
import grazhioImage from '../images/community/grazhio.jpg';
import { ScrollReveal } from '../components/ScrollReveal/ScrollReveal';
import classNames from 'classnames';
import intl from 'react-intl-universal';

const users = [
  {
    name: '<PERSON><PERSON><PERSON>',
    image: frejaImage,
    quote: intl.get('newhome_community_comment_freja'),
    event: 'freja'
  },
  {
    name: '<PERSON><PERSON><PERSON>',
    image: ayaka<PERSON><PERSON>,
    quote: intl.get('newhome_community_comment_ayaka'),
    event: 'ayaka'
  },
  {
    name: '<PERSON>',
    image: julien<PERSON><PERSON>,
    quote: intl.get('newhome_community_comment_grazhio'),
    event: 'grazhio'
  },
  {
    name: 'Ilya Krasnoufimsk',
    image: llyaImage,
    quote: intl.get('newhome_community_comment_llya'),
    event: 'ilya'
  },
  {
    name: 'Grazhio Kujab',
    image: grazhioImage,
    quote: intl.get('newhome_community_comment_julien'),
    event: 'julien'
  },
  {
    name: 'Muhammad Mulyam',
    image: muhammadImage,
    quote: intl.get('newhome_community_comment_muhammad'),
    event: 'muhammad'
  }
];

const CommunitySection: React.FC = () => {
  const listContent = useMemo(
    () => (
      <div className={styles.usersGrid}>
        {users.map((user, index) => (
          <div
            key={index}
            className={styles.userCard}
            onMouseEnter={() => window.gtag && window.gtag('event', `home_body_${user.event}`)}
          >
            <img src={user.image} alt={user.name} className={styles.userImage} />
            <div className={styles.userInfo}>
              <h3 className={styles.userName}>{user.name}</h3>
              {user.quote && <p className={styles.userQuote}>{user.quote}</p>}
            </div>
          </div>
        ))}
      </div>
    ),
    []
  );

  const [scrolled, setScrolled] = useState(false);
  function handleLeftScroll() {
    setScrolled(false);
  }
  function handleRightScroll() {
    setScrolled(true);
  }
  return (
    <section className={classNames(styles.communitySection, scrolled ? styles.scrolled : '')}>
      <div className={styles.container}>
        <ScrollReveal delay={0} duration={0.8} distance="60px" direction="up">
          <h2 className={styles.title}>{intl.get('newhome_community_title')}</h2>
        </ScrollReveal>
        <ScrollReveal delay={200} duration={0.8} distance="60px" direction="up">
          <p className={styles.description}>{intl.get('newhome_community_desc')}</p>
        </ScrollReveal>
        <div className={classNames(styles.containerPC, styles.scrollContainer)}>
          <div className={`${styles.scrollLeft} ${styles.usersGridWrap}`}>{listContent}</div>
        </div>
        <SliderMobile />
      </div>
      <div className={classNames(styles.mask, styles.mask1)}>
        <span
          className={classNames(styles.button, styles.buttonL)}
          onClick={() => {
            handleLeftScroll();
            window.gtag && window.gtag('event', 'home_body_community_left');
          }}
        ></span>
      </div>
      <div className={classNames(styles.mask, styles.mask2)}>
        <span
          className={classNames(styles.button, styles.buttonR)}
          onClick={() => {
            handleRightScroll();
            window.gtag && window.gtag('event', 'home_body_community_right');
          }}
        ></span>
      </div>
    </section>
  );
};

export default CommunitySection;

function SliderMobile() {
  const containerRef = useRef<null | HTMLDivElement>(null);
  const contentRef = useRef<null | HTMLDivElement>(null);
  const [containerWidth, setContainerWidth] = useState(400);
  const [contentWidth, setContentWidth] = useState(500);
  useLayoutEffect(() => {
    if (!containerRef.current || !contentRef.current) {
      return;
    }
    setContainerWidth(containerRef.current.offsetWidth);
    setContentWidth(contentRef.current?.offsetWidth);
  }, []);

  const [activeIndex, setActiveIndex] = useState(0);
  const transform = useMemo(() => {
    if (activeIndex < 3) {
      return `translateX(-${230 * activeIndex}px)`;
    } else {
      return `translateX(-${contentWidth - containerWidth}px)`;
    }
  }, [activeIndex, contentWidth, containerWidth]);

  function handleLeftScroll() {
    setActiveIndex(activeIndex < 3 ? activeIndex + 1 : activeIndex);
  }
  function handleRightScroll() {
    setActiveIndex(activeIndex > 0 ? activeIndex - 1 : activeIndex);
  }
  return (
    <div className={classNames(styles.containerM)} ref={containerRef}>
      <div className={classNames(styles.usersGridWrap)}>
        <div ref={contentRef} className={styles.usersGrid} style={{ transform }}>
          {users.map((user, index) => (
            <div key={index} className={styles.userCard}>
              <img src={user.image} alt={user.name} className={styles.userImage} />
              <div className={styles.userInfo}>
                <h3 className={styles.userName}>{user.name}</h3>
                {user.quote && <p className={styles.userQuote}>{user.quote}</p>}
              </div>
            </div>
          ))}
        </div>
      </div>
      <div className={classNames(styles.buttonWrap)}>
        <span
          className={classNames(styles.button, styles.buttonL, activeIndex === 3 ? styles.disabled : '')}
          onClick={handleRightScroll}
        ></span>
        <span
          className={classNames(styles.button, styles.buttonR, activeIndex === 0 ? styles.disabled : '')}
          onClick={handleLeftScroll}
        ></span>
      </div>
    </div>
  );
}
