import React, { useState, useEffect } from 'react';
import styles from './featuresSection.module.scss';
import { ScrollReveal } from '../components/ScrollReveal/ScrollReveal';
import FeatureImg1 from '../images/FeatureImg1.png';
import FeatureImg2 from '../images/FeatureImg2.png';
import FeatureImg3 from '../images/FeatureImg3.png';
import FeatureImg4 from '../images/FeatureImg4.png';
import intl from 'react-intl-universal';
import FadeImage from './FadeImage';
// import FadeImage from './FadeImage';

// 预加载图片函数
const preloadImages = (images: string[]) => {
  images.forEach(src => {
    const img = new Image();
    img.src = src;
  });
};

const FeaturesSection: React.FC = () => {
  const [activeFeature, setActiveFeature] = useState(0);

  const featureImages = [FeatureImg1, FeatureImg2, FeatureImg3, FeatureImg4];

  const features = [
    {
      title: intl.get('newhome_feat_feature_title1'),
      description: intl.get('newhome_feat_feature_desc1')
    },
    {
      title: intl.get('newhome_feat_feature_title2'),
      description: intl.get('newhome_feat_feature_desc2')
    },
    {
      title: intl.get('newhome_feat_feature_title3'),
      description: intl.get('newhome_feat_feature_desc3')
    },
    {
      title: intl.get('newhome_feat_feature_title4'),
      description: intl.get('newhome_feat_feature_desc4')
    }
  ];

  // 在组件挂载时预加载图片
  useEffect(() => {
    preloadImages([FeatureImg1, FeatureImg2, FeatureImg3, FeatureImg4]);
  }, []);

  return (
    <section className={styles.featuresSection}>
      <div className={styles.container}>
        <ScrollReveal duration={0.8} distance="60px" direction="up" parallax={true} parallaxSpeed={0.88}>
          <div className={styles.leftContent}>
            <ScrollReveal duration={0.8} distance="60px" direction="up">
              <h2 className={styles.title}>{intl.getHTML('newhome_feat_title1')}</h2>
            </ScrollReveal>
            <ScrollReveal delay={200} duration={0.8} distance="60px" direction="up">
              <p className={styles.subtitle}>{intl.getHTML('newhome_feat_desc1')}</p>
            </ScrollReveal>
            <ScrollReveal delay={400} duration={0.8} distance="60px" direction="up">
              <div className={styles.featureImg}>
                <FadeImage
                  src={featureImages[activeFeature]}
                  alt={`GasFree Feature ${activeFeature + 1}`}
                  className={styles.featureImage}
                />
              </div>
            </ScrollReveal>
          </div>
        </ScrollReveal>

        <ScrollReveal delay={600} duration={0.8} distance="60px" direction="up" parallax={true} parallaxSpeed={0.92}>
          <div className={styles.rightContent}>
            {features.map((feature, index) => (
              <div
                key={index}
                className={`${styles.featureItem} ${activeFeature === index ? styles.active : ''}`}
                onMouseEnter={() => setActiveFeature(index)}
                onClick={() => setActiveFeature(index)}
              >
                <h3 className={styles.featureTitle}>{feature.title}</h3>
                <p className={styles.featureDescription} dangerouslySetInnerHTML={{ __html: feature.description }}></p>
              </div>
            ))}
          </div>
        </ScrollReveal>
      </div>
    </section>
  );
};

export default FeaturesSection;
