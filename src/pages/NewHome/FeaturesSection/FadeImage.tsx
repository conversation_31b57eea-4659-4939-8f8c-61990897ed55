import React, { useState, useEffect, useRef, CSSProperties } from 'react';

interface FadeImageProps {
  src: string;
  alt?: string;
  duration?: number; // 动画时长，毫秒
  className?: string;
  style?: CSSProperties;
}

const FadeImage: React.FC<FadeImageProps> = ({ src, alt = '', duration = 200, className = '', style = {} }) => {
  const [displaySrc, setDisplaySrc] = useState(src);
  const [fadeState, setFadeState] = useState<'idle' | 'fade-out' | 'fade-in'>('idle');
  const prevSrc = useRef(src);

  useEffect(() => {
    if (src !== prevSrc.current) {
      setFadeState('fade-out');
    }
  }, [src]);

  // 监听淡出动画结束，切换图片并淡入
  useEffect(() => {
    if (fadeState === 'fade-out') {
      const timer = setTimeout(() => {
        setDisplaySrc(src);
        setFadeState('fade-in');
        prevSrc.current = src;
      }, duration);
      return () => clearTimeout(timer);
    }
    if (fadeState === 'fade-in') {
      const timer = setTimeout(() => {
        setFadeState('idle');
      }, duration);
      return () => clearTimeout(timer);
    }
  }, [fadeState, src, duration]);

  let opacity = 1;
  if (fadeState === 'fade-out') opacity = 0;
  if (fadeState === 'fade-in') opacity = 1;

  return (
    <img
      src={displaySrc}
      alt={alt}
      className={className}
      style={{
        ...style,
        opacity,
        transition: `opacity ${duration}ms ease`,
        display: 'block',
        width: '100%',
        height: '100%',
        objectFit: 'contain'
      }}
    />
  );
};

export default FadeImage;
