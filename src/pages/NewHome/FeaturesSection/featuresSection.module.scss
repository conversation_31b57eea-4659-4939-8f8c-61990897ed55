.featuresSection {
  width: 1200px;
  margin: -150px auto 0;
  padding: 0;
  position: relative;
  z-index: 20;

  .container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: flex-start;
    padding: 0 0px;
  }

  .leftContent {
    position: relative;
    z-index: 1;

    .title,
    .title span {
      font-family: 'Sora';
      font-size: 40px;
      font-weight: 800;
      line-height: 110%;
      color: #ffffff;
      margin: 0 0 24px;
      text-align: left;
      br {
        display: inline;
      }
    }

    .subtitle,
    .subtitle span {
      font-family: 'Sora';
      font-size: 18px;
      font-weight: 400;
      line-height: 150%;
      color: rgba(255, 255, 255, 0.6);
      margin: 0 0 40px;
      text-align: left;
      max-width: 380px;
      br {
        display: inline;
      }
    }

    .featureImg {
      width: 400px;
      height: 280px;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;

      .featureImage {
        width: 280px;
        height: 280px;
        object-fit: contain;
      }
    }
  }

  .rightContent {
    display: flex;
    flex-direction: column;
    gap: 0; // 移除原来的gap，改用margin和border来控制间距
    margin-top: -40px;
    position: relative;
    z-index: 1;

    .featureItem {
      padding: 40px 0;
      border-bottom: 1px solid rgba(255, 255, 255, 0.3);
      transition: all 0.3s ease;
      opacity: 0.7;

      &:last-child {
        border-bottom: none;
      }

      &:hover {
        opacity: 1;
        transform: translateX(8px);
        .featureTitle {
          color: #33ffa9;
        }
      }

      &.active {
        opacity: 1;

        .featureTitle {
          color: #33ffa9;
        }
      }

      .featureTitle {
        font-family: 'Sora';
        font-size: 24px;
        font-weight: 800;
        line-height: 120%;
        color: rgba(51, 255, 169, 0.7);
        margin: 0 0 16px;
        text-align: left;
        transition: color 0.3s ease;
      }

      .featureDescription {
        font-family: 'Sora';
        font-size: 14px;
        font-weight: 400;
        line-height: 150%;
        color: rgba(255, 255, 255, 0.7);
        margin: 0;
        text-align: left;
        :global(.highlight) {
          color: #fff;
          font-weight: 700;
        }
      }
    }
  }
}

@media screen and (max-width: 798px) {
  .featuresSection {
    width: auto;
    margin: 0;
    padding: 0 20px 100px;
    .container {
      display: flex;
      flex-direction: column;
    }
    .leftContent {
      .title,
      .title span {
        font-size: 24px;
        font-weight: 800;
        margin-bottom: 10px;
      }
      .subtitle,
      .subtitle span {
        font-size: 11px;
        font-weight: 400;
        padding-right: 50px;
        margin-bottom: 40px;
        br {
          display: none;
        }
      }
      .featureImg {
        justify-content: start;
        align-items: start;
        width: 120px;
        height: 120px;
        .featureImage {
          width: 120px;
          height: 120px;
          margin-bottom: 40px;
        }
      }
    }
    .rightContent {
      .featureItem {
        padding: 0 0 30px;
        & + .featureItem {
          margin-top: 30px;
        }
        &:last-child {
          padding-bottom: 0;
        }
      }
    }
  }
}
