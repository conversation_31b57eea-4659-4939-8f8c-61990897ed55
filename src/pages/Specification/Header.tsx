import React, { useState, useRef, useEffect } from 'react';
import styles from './specification.module.scss';
import {
  ProductsSubMenus,
  DevelopersSubMenu,
  ResourcesSubMenu,
  ContractsSubMenu,
  CommunitySubMenu
} from '../NewHome/utils/const';
import { getLang, setLang } from '@/utils/select-lang';
import logo from '../NewHome/images/logo.svg';
import intl from 'react-intl-universal';

const NAVS = [
  { key: 'products', label: intl.get('newhome_nav_products'), menus: ProductsSubMenus },
  { key: 'developers', label: intl.get('newhome_nav_developers'), menus: DevelopersSubMenu },
  { key: 'resources', label: intl.get('newhome_nav_resource'), menus: ResourcesSubMenu },
  { key: 'contacts', label: intl.get('newhome_nav_contact'), menus: ContractsSubMenu },
  { key: 'community', label: intl.get('newhome_nav_community'), menus: CommunitySubMenu }
];

const LANGS = [
  { code: 'en-US', label: 'EN', displayName: 'English' },
  { code: 'zh-TC', label: '繁中', displayName: '繁体中文' },
  { code: 'zh-CN', label: '简中', displayName: '简体中文' }
];

function isDescendant(parent: Node, child: Node): boolean {
  let node: Node | null = child;
  while (node) {
    if (node === parent) return true;
    node = node.parentNode as Node | null;
  }
  return false;
}

export default function DocHeader() {
  const [openMenu, setOpenMenu] = useState<string | null>(null);
  const [langOpen, setLangOpen] = useState(false);
  const [currentLang, setCurrentLang] = useState<string>(() => getLang());
  const searchInputRef = useRef<HTMLInputElement>(null);
  const langBoxRef = useRef<HTMLDivElement>(null);
  const [langHoverLock, setLangHoverLock] = useState(false);

  // 语言切换
  const handleLangSelect = (lang: { code: string }) => {
    setCurrentLang(lang.code);
    setLang(lang.code as 'en-US' | 'zh-CN' | 'zh-TC');
    setLangOpen(false);
    // 新增：切换后短暂禁用hover，防止hover立即又打开
    setLangHoverLock(true);
    setTimeout(() => setLangHoverLock(false), 300);
  };

  // 监听语言变化，确保组件状态同步
  useEffect(() => {
    const currentStoredLang = getLang();
    if (currentStoredLang !== currentLang) {
      setCurrentLang(currentStoredLang);
    }
  }, [currentLang]);

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (langBoxRef.current && !isDescendant(langBoxRef.current, event.target as Node)) {
        setLangOpen(false);
      }
      setOpenMenu(null);
    };
    document.body.addEventListener('click', handleClickOutside, { capture: true });
    return () => {
      document.body.removeEventListener('click', handleClickOutside, { capture: true });
    };
  }, []);

  // Ctrl+K 聚焦搜索框
  useEffect(() => {
    const handler = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key.toLowerCase() === 'k') {
        e.preventDefault();
        searchInputRef.current?.focus();
      }
    };
    window.addEventListener('keydown', handler);
    return () => window.removeEventListener('keydown', handler);
  }, []);

  return (
    <header className={styles.docHeader}>
      <div className={styles.docHeaderInner}>
        {/* 左侧logo */}
        <a className={styles.docHeaderLogo} href="/">
          <img src={logo} alt="logo" className={styles.docHeaderLogoIcon} />
        </a>
        {/* 中间菜单 */}
        <nav className={styles.docHeaderNav}>
          {NAVS.map(nav => (
            <div
              key={nav.key}
              className={styles.docHeaderNavItem}
              onMouseEnter={() => setOpenMenu(nav.key)}
              onMouseLeave={() => setOpenMenu(null)}
            >
              <span className={styles.docHeaderNavLabel}>{nav.label}</span>
              {openMenu === nav.key && nav.menus && (
                <div className={styles.docHeaderDropdown}>
                  {nav.menus.map(item => {
                    const isJoin =
                      typeof item.label === 'string' && (item.label.includes('Join') || item.label.includes('加入'));
                    return (
                      <a
                        key={item.href}
                        href={item.href}
                        target={item.target || '_self'}
                        className={
                          styles.docHeaderDropdownItem + (isJoin ? ' ' + styles.docHeaderDropdownItemJoin : '')
                        }
                        rel={item.target === '_blank' ? 'noopener noreferrer' : undefined}
                      >
                        {isJoin && (
                          <span className={styles.docHeaderJoinIcon}>
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                              <rect width="16" height="16" rx="4" fill="#33ffa9" />
                              <path d="M8 4v8M4 8h8" stroke="#222" strokeWidth="2" strokeLinecap="round" />
                            </svg>
                          </span>
                        )}
                        {typeof item.label === 'string' ? item.label : item.label}
                      </a>
                    );
                  })}
                </div>
              )}
            </div>
          ))}
        </nav>
        {/* 右侧搜索和语言切换 */}
        <div className={styles.docHeaderRight}>
          {/* <div className={styles.docHeaderSearchBox}>
            <span className={styles.docHeaderSearchIcon}>
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                <circle cx="7.5" cy="7.5" r="5.5" stroke="#fff" strokeWidth="1.5" />
                <path d="M13 13L11 11" stroke="#fff" strokeWidth="1.5" strokeLinecap="round" />
              </svg>
            </span>
            <input className={styles.docHeaderSearchInput} placeholder="Search" ref={searchInputRef} />
            <span className={styles.docHeaderShortcut}>
              <b>Ctrl</b> + <b>K</b>
            </span>
          </div> */}
          <div className={styles.docHeaderLangBox} ref={langBoxRef}>
            <div
              className={styles.docHeaderLangButton}
              onMouseEnter={() => setLangOpen(true)}
              onMouseLeave={() => setLangOpen(false)}
            >
              <span className={styles.docHeaderLangIcon}>
                <div className={styles.globeIcon}></div>
              </span>
              <span className={styles.docHeaderLangText}>{LANGS.find(l => l.code === currentLang)?.label || 'EN'}</span>
            </div>
            <div
              className={langOpen ? styles.docHeaderLangDropdown : styles.docHeaderLangDropdown + ' ' + styles.hidden}
              onMouseEnter={() => setLangOpen(true)}
              onMouseLeave={() => setLangOpen(false)}
            >
              {LANGS.map(lang => (
                <div
                  key={lang.code}
                  className={
                    currentLang === lang.code
                      ? styles.docHeaderLangOption + ' ' + styles.active
                      : styles.docHeaderLangOption
                  }
                  onClick={e => {
                    e.stopPropagation();
                    handleLangSelect(lang);
                  }}
                >
                  <span>{lang.displayName}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}
