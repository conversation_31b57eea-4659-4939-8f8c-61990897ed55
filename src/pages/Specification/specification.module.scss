// ===== 基础变量 =====
$primary-color: #2d4bf6;
$primary-hover: #2336a3;
$text-primary: #07094c;
$text-secondary: rgba(7, 9, 76, 0.7);
$text-muted: #888;
$text-content: rgba(7, 9, 76, 0.7);
$bg-primary: #fff;
$bg-secondary: #f8f8f8;
$bg-tertiary: #f0f4ff;
$border-color: #e0e0e0;
$border-light: #e5eaf3;
$success-color: #00f6a0;
$warning-color: #d32f2f;
$code-bg: #f8f9fb;
$code-color: #b84c00;

// ===== 布局样式 =====
.docPage {
  position: relative;
  min-height: 100vh;
  background: #f8f8f8;
  display: flex;
  flex-direction: column;
  overflow: visible !important;
}

.docPageBg {
  position: absolute;
  left: 50%;
  top: 0;
  width: 930px;
  height: 316px;
  z-index: 0;
  pointer-events: none;
  background: url('./images/bg.png') center bottom/cover no-repeat;
}

.docMain {
  max-width: 1440px;
  margin: 0 auto;
  display: flex;
  padding: 0 20px 40px;
  min-height: 100vh;
}

// ===== 侧边栏样式 =====
.docSidebar {
  width: 340px;
  background: $bg-secondary;
  // border-radius: 12px;
  padding: 40px 20px;
  position: sticky;
  top: 56px;
  align-self: flex-start;
  min-height: 100vh;
}

.docSidebarItem {
  color: #181c32;
  font-size: 18px;
  border-radius: 8px;
  cursor: pointer;
  line-height: 24px;
  transition:
    background 0.2s,
    color 0.2s;
  margin-bottom: 8px;
  background: none;
  font-weight: 700;
  padding-right: 12px;
  text-decoration: none;
  display: block;
  width: auto;

  &.hasChildren {
    font-weight: 700;
    color: $primary-color;
  }

  &.active {
    background: rgba(7, 9, 76, 0.05);
    border-radius: 6px;
    color: $primary-color;
    font-weight: 700;
  }

  &:hover {
    background: $bg-tertiary;
    color: $primary-color;
  }

  // 子项样式
  &.child {
    font-weight: 500;
    color: $text-primary;
    background: none;
    border-radius: 4px;
    margin-bottom: 2px;
    margin-left: 0;
    padding-left: 28px !important;
    font-size: 14px;
    text-align: left;

    &.childActive {
      background: #e6eaf3;
      color: $primary-color;
      font-weight: 700;
    }
  }
}

// ===== 主内容区域样式 =====
.docContent {
  flex: 1;
  max-width: 960px;
  padding: 40px 48px 40px;
  min-width: 0;
  box-shadow: 0 2px 8px 0 rgba(82, 109, 249, 0.04);
  scroll-margin-top: 66px;
  font-size: 16px;
  color: $text-primary;
  line-height: 2;
  text-align: left;
  border-left: 1px solid rgba(7, 9, 76, 0.1);

  // 为所有标题添加滚动偏移
  h1,
  h2,
  h3 {
    scroll-margin-top: 66px;
  }

  // 标题样式
  h1,
  h2,
  h3 {
    text-align: left;
    text-transform: capitalize;
  }

  // 段落和列表样式
  p {
    font-size: 14px;
    color: $text-secondary;
    line-height: 1.7;
    margin-bottom: 16px;
  }

  ul,
  ol {
    margin: 0 0 16px 16px;
    font-size: 16px;
    color: $text-secondary;
    line-height: 1.7;
    padding-left: 18px;
  }

  ol li {
    list-style: none;
  }

  ul li {
    margin-bottom: 6px;
    list-style: disc;
    font-size: 16px;
    color: $text-secondary;
    line-height: 30px;
  }

  // 链接样式
  a {
    color: #3347ef;
    text-decoration: underline;
    font-size: 16px;
    font-weight: 400;
    transition: color 0.18s;
    word-break: break-all;

    &:hover {
      color: $primary-hover;
    }
  }

  // 强调文本样式
  b,
  strong {
    font-weight: 600;
    color: #07094c;
    font-size: 16px;
    line-height: 30px;

    &.docStrongRed {
      color: $warning-color;
      font-weight: 700;
    }
  }

  i,
  em {
    font-style: italic;
    color: #444;
  }

  // 代码样式
  code {
    background: $bg-tertiary;
    color: $text-primary;
    border-radius: 4px;
    font-size: 14px;
    font-family: 'Menlo', 'Consolas', monospace;
  }

  pre {
    background: #fff;
    color: $text-primary;
    border-radius: 8px;
    padding: 20px;
    font-size: 15px;
    font-family: 'Fira Mono', 'Consolas', 'Menlo', monospace;
    margin: 20px 0;
    overflow-x: auto;

    code {
      font-family: 'Fira Mono', 'Consolas', 'Menlo', monospace;
      color: $code-color;
      font-size: 15px;
      background: none;
    }
  }

  // 图片和图表样式
  figure {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 24px 0;
  }

  img {
    max-width: 100%;
    border-radius: 8px;
    margin: 0 auto;
    display: block;
  }

  figcaption {
    font-size: 13px;
    color: $text-muted;
    margin-top: 8px;
    text-align: center;
  }

  // 引用样式
  blockquote {
    border-left: 3px solid $bg-tertiary;
    background: #f8faff;
    color: $text-secondary;
    margin: 20px 0;
    padding: 12px 20px;
    border-radius: 6px;
    font-size: 14px;
  }
}

// ===== 文档标题和版本信息 =====
.docTitle {
  font-size: 36px;
  font-weight: 700;
  text-align: center;
  margin-top: 32px;
  line-height: 32px;
  margin-bottom: 0;
  color: $text-primary;
}

.docDivider {
  margin: 32px 0 24px 0;
  border: none;
  border-top: 1px solid #ececec;
}

.docVersion {
  text-align: right;
  color: $text-muted;
  font-size: 16px;
  margin-bottom: 32px;
}

// ===== 章节标题样式 =====
.docSectionTitle {
  font-size: 32px;
  font-weight: 700;
  margin-top: 60px;
  margin-bottom: 16px;
  line-height: 24px;
  color: $text-primary;
  scroll-margin-top: 66px;
  text-align: left;
  text-transform: capitalize;
}

.docSubTitle {
  font-size: 24px;
  font-weight: 600;
  margin: 32px 0 16px 0;
  color: $text-primary;
  scroll-margin-top: 66px;
  text-align: left;
  text-transform: capitalize;
}

// ===== 内容描述样式 =====
.docSectionDesc {
  font-size: 16px;
  color: $text-content;
  margin-bottom: 24px;
  line-height: 30px;
  text-align: left;
}

// ===== 概览部分样式 =====
.docOverviewFigure {
  text-align: center;
  margin: 32px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.docOverviewImg {
  max-width: 100%;
  height: auto;
  margin: 0 auto;
  width: 460px;
  height: 368px;
}

.docOverviewCaption {
  color: $text-content;
  font-size: 13px;
  margin-top: 8px;
  text-align: center;
}

.docOverviewRoles {
  font-size: 16px;
  color: $text-content;
  line-height: 30px;
  text-align: left;

  > div {
    margin-bottom: 12px;
    font-size: 16px;
    color: $text-content;
    line-height: 30px;
  }
}

// ===== 列表样式 =====
.docSectionList {
  font-size: 16px;
  color: $text-content;
  line-height: 2;
  margin-left: 32px;
  margin-bottom: 32px;
  padding-left: 16px;

  > li {
    margin-bottom: 10px;
  }

  ul {
    margin-top: 8px;
    margin-bottom: 8px;
    margin-left: 20px;
  }

  ol {
    margin-top: 8px;
    margin-bottom: 8px;
    margin-left: 20px;
  }

  a {
    color: $primary-color;
    text-decoration: underline;
    word-break: break-all;
    transition: color 0.18s;

    &:hover {
      color: $primary-hover;
    }
  }

  b {
    font-weight: 700;
  }
}

// ===== 表格样式 =====
.docTable {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 8px;
  font-size: 16px;
  border: 1px solid $border-color;

  th,
  td {
    border: 1px solid $border-color;
    padding: 10px 16px;
    text-align: left;
  }

  th {
    background: $bg-secondary;
    font-weight: 700;
  }
}

.docTableCaption {
  color: $text-muted;
  font-size: 15px;
  margin-bottom: 24px;
}

// ===== 代码块样式 =====
.docCodeBlock {
  background: #fff;
  color: $text-primary;
  border-radius: 8px;
  padding: 20px;
  font-size: 15px;
  font-family: 'Fira Mono', 'Consolas', 'Menlo', monospace;
  margin: 20px 0;
  overflow-x: auto;

  code {
    font-family: 'Fira Mono', 'Consolas', 'Menlo', monospace;
    color: $code-color;
    font-size: 15px;
    background: none;
  }
}

.docInlineCode {
  font-family: 'Fira Mono', 'Consolas', 'Menlo', monospace;
  color: $code-color;
  background: $bg-secondary;
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 15px;
}

// ===== 字段描述样式 =====
.docFieldDesc {
  font-size: 16px;
  color: #07094c;
  margin: 16px 0 8px 0;
  font-weight: 700;
  text-align: left;
}

// ===== 特殊样式 =====
.docSectionNote {
  color: $text-muted;
  font-size: 15px;
  margin: 12px 0 24px 0;
}

.docWarn {
  color: $warning-color;
  font-size: 16px;
  font-weight: 700;
  margin-bottom: 8px;
}

.docLink {
  color: $primary-color;
  text-decoration: underline;
  transition: color 0.18s;

  &:hover {
    color: $primary-hover;
  }
}

// ===== 头部样式（保留原有结构） =====
.docHeader {
  width: 100%;
  height: 56px;
  background: #111;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  z-index: 100;
  position: sticky;
  top: 0;
}

.docHeaderInner {
  width: 100%;
  max-width: 1440px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  height: 56px;
  justify-content: space-between;
  box-sizing: border-box;
}

.docHeaderLogo {
  display: flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  margin-left: 24px;
}

.docHeaderLogoIcon {
  width: 124px;
  height: 40px;
  object-fit: contain;
  display: block;
  transition: opacity 0.2s ease;
}

.docHeaderLogoText {
  color: $success-color;
  font-size: 20px;
  font-weight: 700;
  letter-spacing: 0.5px;
  line-height: 1;
}

.docHeaderNav {
  display: flex;
  gap: 40px;
  margin-left: 48px;
}

.docHeaderNavItem {
  color: #fff;
  font-size: 15px;
  font-weight: 600;
  text-decoration: none;
  transition: color 0.2s;
  line-height: 56px;
  padding: 0 2px;
  position: relative;
  cursor: pointer;
  width: 96px;
  text-align: center;
  box-sizing: border-box;

  &:hover .docHeaderNavLabel,
  &:focus .docHeaderNavLabel {
    color: $success-color;
  }
}

.docHeaderNavLabel {
  color: #fff;
  font-size: 15px;
  font-weight: 600;
  transition: color 0.2s;
}

.docHeaderRight {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-right: 24px;
}

.docHeaderSearchBox {
  display: flex;
  align-items: center;
  background: #222;
  border-radius: 8px;
  padding: 0 10px 0 8px;
  height: 36px;
  min-width: 180px;
  margin-right: 8px;
  box-sizing: border-box;
}

.docHeaderSearchIcon {
  display: flex;
  align-items: center;
  margin-right: 6px;
}

.docHeaderSearchInput {
  background: transparent;
  border: none;
  outline: none;
  color: #fff;
  font-size: 15px;
  width: 70px;
  margin-right: 8px;
  font-family: inherit;

  &::placeholder {
    color: #888;
    opacity: 1;
  }
}

.docHeaderShortcut {
  background: #fff;
  color: #222;
  border-radius: 4px;
  font-size: 13px;
  padding: 2px 6px;
  margin-left: 2px;
  font-weight: 700;
  letter-spacing: 1px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.04);
  display: flex;
  align-items: center;
  height: 22px;
}

.docHeaderLangBox {
  position: relative;
  z-index: 200;
  display: inline-block;
}

.docHeaderLangIcon {
  display: flex;
  align-items: center;
  margin-right: 6px;
}

.docHeaderLangText {
  color: #fff;
  font-size: 15px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.docHeaderLangDropdown {
  position: absolute;
  top: 100%;
  left: 0;
  min-width: 120px;
  background: #222;
  border-radius: 8px;
  box-shadow: 0 4px 24px 0 rgba(0, 0, 0, 0.18);
  border: 2px solid #444;
  padding: 8px 0;
  z-index: 200;
  opacity: 1;
  pointer-events: auto;
  transition: opacity 0.2s;

  &.hidden {
    opacity: 0;
    pointer-events: none;
  }
}

.docHeaderLangOption {
  color: #fff;
  font-size: 15px;
  padding: 8px 20px;
  cursor: pointer;
  transition:
    background 0.2s,
    color 0.2s;

  &.active,
  &:hover {
    background: #333;
    color: $success-color;
  }
}

.docHeaderLangButton {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 3px;
  padding: 8px 10px;
  background: #222;
  border-radius: 8px;
  cursor: pointer;
  min-width: 82px;
  transition: background 0.2s;

  &:hover {
    background: #333;
  }
}

// ===== 下拉菜单样式 =====
.gasfreeDropdown {
  position: absolute;
  top: 44px;
  left: 50%;
  transform: translateX(-50%);
  min-width: 220px;
  background: #222;
  border-radius: 8px;
  box-shadow: 0 4px 24px 0 rgba(0, 0, 0, 0.18);
  border: 2px solid #444;
  padding: 12px 0;
  z-index: 100;
  display: flex;
  flex-direction: column;
  gap: 0;
}

.gasfreeDropdownItem {
  color: #fff;
  font-size: 16px;
  padding: 10px 20px;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition:
    background 0.2s,
    color 0.2s;
  font-weight: 400;

  &:hover {
    background: #333;
    color: $success-color;
  }

  &.join {
    color: $success-color;
    font-weight: 600;
  }

  .joinIcon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 18px;
    height: 18px;
    background: $success-color;
    border-radius: 4px;
    margin-right: 4px;
  }
}

.docHeaderDropdown {
  position: absolute;
  left: 50%;
  transform: translateX(-50%) translateY(-10px);
  margin-top: 8px;
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.15);
  background: #212121;
  opacity: 1;
  z-index: 1000;
  padding: 10px;
  min-width: 220px;
  box-shadow: 0 6px 24px 0 rgba(0, 0, 0, 0.18);
  display: flex;
  flex-direction: column;
  gap: 0;
}

.docHeaderDropdownItem {
  display: block;
  padding: 0px 10px;
  color: #fff;
  text-align: left;
  font-size: 14px;
  line-height: 30px;
  text-decoration: none;
  border-radius: 6px;
  white-space: pre;
  word-wrap: none;
  transition:
    background 0.2s,
    color 0.2s;
  border: none;
  background: none;
  cursor: pointer;
  font-weight: 400;

  & + & {
    margin-top: 10px;
  }

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #33ffa9;
  }

  &--join {
    color: #33ffa9 !important;
    font-weight: 600;
    display: flex;
    align-items: center;

    &:hover {
      background: rgba(51, 255, 169, 0.12) !important;
      color: #33ffa9 !important;
    }
  }
}

.docHeaderJoinIcon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  margin-right: 8px;
  flex-shrink: 0;
}

.globeIcon {
  width: 16px;
  height: 16px;
  background: url('./images/global.svg') no-repeat center/contain;
  display: block;
}
