import React, { useState } from 'react';
import styles from './specification.module.scss';
import Header from './Header';
import overviewImg from './images/overview.jpg';
import arrowSvg from './images/arrow.svg';

// 多级目录结构，补全 children
const sidebarList = [
  { id: 'overview', label: 'Overview' },
  { id: 'auth', label: 'Authorization Process' },
  {
    id: 'sig',
    label: 'Signature Algorithm And Provider Endpoint',
    children: [
      { id: 'sig-params', label: 'Parameters' },
      { id: 'sig-construct', label: 'Authorization Construction and Signature' },
      { id: 'sig-endpoint', label: 'Endpoint' },
      { id: 'sig-faucet', label: 'Faucet' }
    ]
  },
  { id: 'notes', label: 'Notes' },
  {
    id: 'apis',
    label: 'APIs',
    children: [
      { id: 'api-auth', label: 'API Authentication' },
      { id: 'api-format', label: 'API Format Definition' },
      { id: 'api-token-all', label: 'GET /api/v1/config/token/all' },
      { id: 'api-provider-all', label: 'GET /api/v1/config/provider/all' },
      { id: 'api-address', label: 'GET /api/v1/address/{accountAddress}' },
      { id: 'api-submit', label: 'POST /api/v1/gasfree/submit' },
      { id: 'api-trace', label: 'GET /api/v1/gasfree/{traceId}' }
    ]
  }
];

interface SidebarItem {
  id: string;
  label: string;
  children?: SidebarItem[];
}
function findParentIds(list: SidebarItem[], targetId: string, path: string[] = []): string[] | null {
  for (const item of list) {
    if (item.id === targetId) return path;
    if (item.children) {
      const res = findParentIds(item.children, targetId, [...path, item.id]);
      if (res) return res;
    }
  }
  return null;
}

export default function Specification() {
  const [active, setActive] = useState('overview');
  const [openIds, setOpenIds] = useState<Set<string>>(() => new Set());

  // 递归渲染多级导航，只有active高亮
  const renderSidebar = (list: SidebarItem[], level = 1) => (
    <div>
      {list.map(item => {
        const isActive = active === item.id;
        const isOpen = openIds.has(item.id);
        const hasChildren = !!item.children && item.children.length > 0;
        return (
          <div key={item.id}>
            <a
              href={`#${item.id}`}
              className={`${styles.docSidebarItem} ${isActive ? styles.active : ''}`}
              style={{
                paddingLeft: 20 + (level - 1) * 20,
                paddingRight: 12,
                fontWeight: level === 1 ? 700 : 500,
                fontSize: level === 1 ? 18 : 16,
                borderRadius: 8,
                marginBottom: 8,
                textAlign: 'left',
                cursor: 'pointer',
                display: 'block',
                textDecoration: 'none',
                width: 'auto'
              }}
              onClick={e => {
                if (hasChildren) {
                  e.preventDefault();
                  setActive(item.id);
                  setOpenIds(prev => {
                    const newSet = new Set(prev);
                    if (newSet.has(item.id)) {
                      newSet.delete(item.id);
                    } else {
                      newSet.add(item.id);
                    }
                    return newSet;
                  });
                } else {
                  setActive(item.id);
                  const parents = findParentIds(sidebarList, item.id) || [];
                  setOpenIds(prev => {
                    const newSet = new Set(prev);
                    parents.forEach(pid => newSet.add(pid));
                    return newSet;
                  });
                }
              }}
            >
              <span>{item.label}</span>
              {hasChildren && (
                <span
                  style={{
                    float: 'right',
                    fontWeight: 'normal',
                    color: '#bbb',
                    marginLeft: 8,
                    display: 'flex',
                    alignItems: 'center',
                    height: 18
                  }}
                >
                  <img
                    src={arrowSvg}
                    alt="arrow"
                    style={{
                      width: 10,
                      height: 10,
                      transition: 'transform 0.2s',
                      transform: isOpen ? 'rotate(90deg)' : 'rotate(0deg)'
                    }}
                  />
                </span>
              )}
            </a>
            {hasChildren && isOpen && <div>{renderSidebar(item.children || [], level + 1)}</div>}
          </div>
        );
      })}
    </div>
  );

  return (
    <div className={styles.docPage}>
      <div className={styles.docPageBg}></div>
      <Header />
      <div className={styles.docMain}>
        <aside className={styles.docSidebar}>{renderSidebar(sidebarList)}</aside>
        <main className={styles.docContent}>
          <h1 className={styles.docTitle}>GasFree Developer Documentation</h1>
          <hr className={styles.docDivider} />
          <div className={styles.docVersion}>Ver: 1.0.2</div>
          <h2 id="overview" className={styles.docSectionTitle}>
            1. Overview
          </h2>
          <div className={styles.docSectionDesc}>
            GasFree aims to provide users with a TRC-20/ERC-20 transfer solution that does not require a native token to
            pay for transaction gas fees. It specifically includes four roles: GasFree accounts, Service-Providers,
            wallets, and users, as shown in Figure 1. below.
          </div>
          <figure className={styles.docOverviewFigure}>
            <img src={overviewImg} alt="GasFree Framework" className={styles.docOverviewImg} />
            <figcaption className={styles.docOverviewCaption}>Figure 1. GasFree Framework</figcaption>
          </figure>
          <div className={styles.docOverviewRoles}>
            <div>
              <strong>GasFree Account:</strong> Generated according to a specific algorithm. Its permissions are
              controlled by the user's EOA (Externally Owned Account) address. The user can sign a signature to
              authorize GasFree transfer of this account.
            </div>
            <div>
              <strong>Service-Provider:</strong> The GasFree service-provider is responsible for collecting users'
              GasFree transfer authorizations, submitting them to the blockchain, and paying the Gas fees on behalf of
              the users. The Service-Provider may charge a certain handling fee after the transaction is completed.
            </div>
            <div>
              <strong>Wallet:</strong> After connecting to the GasFree service, the wallet provides end-users with
              interface functions such as fund inquiry for GasFree accounts and signature for transfer authorization.
            </div>
          </div>

          <h2 id="auth" className={styles.docSectionTitle}>
            2. Authorization Process
          </h2>
          <div className={styles.docSectionDesc}>
            Integrating GasFree into the wallet involves processes such as constructing transfer authorization, signing
            the transfer authorization, and the provider submitting the authorization to the blockchain on behalf of the
            user. For detailed information about the Provider API interface, please refer to Section 5 of this document.
            After the integration, the main interaction process is as follows:
          </div>
          <ol className={styles.docSectionList}>
            <li>
              <strong>1. Prerequisite:</strong>
              <ul>
                <li>
                  The Service-Provider supports GasFree transfers for multiple types of tokens. Call{' '}
                  <a href="/api/v1/config/token/all" target="_blank" rel="noopener noreferrer">
                    /api/v1/config/token/all
                  </a>{' '}
                  to obtain the list of supported tokens.
                </li>
                <li>
                  When a token that the Provider does not currently support is transferred into a GasFree account, the
                  user uses the withdrawal page provided by GasFree official to withdraw the token to their EOA
                  (Externally Owned Account) address. The link to the withdrawal page is:{' '}
                  <a href="https://gasfree.io/withdraw" target="_blank" rel="noopener noreferrer">
                    https://gasfree.io/withdraw
                  </a>
                </li>
                <li>
                  GasFree supports multiple Service-Providers. Just make a call to{' '}
                  <a href="/api/v1/config/provider/all" target="_blank" rel="noopener noreferrer">
                    /api/v1/config/provider/all
                  </a>{' '}
                  to obtain the list of available Service-Providers.
                </li>
                <li>
                  The GasFree account is in an inactive state by default. An inactive GasFree account will be
                  automatically activated during the first GasFree transfer. When activating the GasFree account, an
                  additional 'activation fee' will be charged. For subsequent GasFree transfer authorizations, only the
                  'transfer fee' will be charged.
                </li>
              </ul>
            </li>
            <li>
              <strong>2. Preparation:</strong>
              <ul>
                <li>
                  When a user conducts a GasFree transfer authorization, first call{' '}
                  <a href="/api/v1/address/{accountAddress}" target="_blank" rel="noopener noreferrer">
                    /api/v1/address/&#123;accountAddress&#125;
                  </a>{' '}
                  to query his/her GasFree account info, including status, balance, and current nonce. Based on this
                  info, construct a GasFree transfer authorization.
                </li>
              </ul>
            </li>
            <li>
              <strong>3. Sign authorization:</strong>
              <ul>
                <li>
                  The signature algorithm for GasFree transfer authorization is designed to support subsequent upgrades
                  of the signature algorithm. The signature algorithm of the current version is compatible with the
                  EIP712 specification. For the specific signature algorithm of the authorization, please refer to
                  Section 3.2.
                </li>
              </ul>
            </li>
            <li>
              <strong>4. Submit authorization:</strong>
              <ul>
                <li>
                  Use{' '}
                  <a href="/api/v1/gasfree/submit" target="_blank" rel="noopener noreferrer">
                    /api/v1/gasfree/submit
                  </a>{' '}
                  to send the signed GasFree transfer authorization to the Service-Provider.
                </li>
              </ul>
            </li>
            <li>
              <strong>5. Handling of provider response:</strong>
              <ul>
                <li>
                  After receiving the GasFree transfer authorization submitted by the user, the provider will conduct a
                  verification and immediately return the verification result to the wallet. The wallet should handle
                  the response from the Provider:
                  <ol>
                    <li style={{ listStyle: 'decimal' }}>
                      If successful, notify the user the authorization verification has passed, and include a globally
                      unique authorization <strong>traceId</strong>. Subsequently, the on-chain status of this
                      authorization can be tracked based on this <strong>traceId</strong>.
                    </li>
                    <li style={{ listStyle: 'decimal' }}>
                      If failed, notify the user of the failure, stating that the authorization has been discarded and
                      this request will not trigger an actual transaction on the blockchain.
                    </li>
                  </ol>
                </li>
              </ul>
            </li>
            <li>
              <strong>Monitoring of the transfer authorization:</strong>
              <ul>
                <li>
                  Call{' '}
                  <a href="/api/v1/gasfree/{traceId}" target="_blank" rel="noopener noreferrer">
                    /api/v1/gasfree/&#123;traceId&#125;
                  </a>{' '}
                  to query the status of the GasFree transfer authorization.
                </li>
              </ul>
            </li>
          </ol>

          <h2 id="sig" className={styles.docSectionTitle}>
            3. Signature Algorithm and Provider Endpoint
          </h2>

          <h3 id="sig-params" className={styles.docSubTitle}>
            3.1 Parameters
          </h3>
          <div className={styles.docSectionDesc}>
            The GasFree transfer authorization involves the following parameters. Their values on the TRON mainnet and
            the Nile testnet are shown in Table 1.
          </div>
          <table className={styles.docTable}>
            <thead>
              <tr>
                <th>Parameter</th>
                <th>TRON - mainnet</th>
                <th>TRON - Nile testnet</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>chainId</td>
                <td>728126428</td>
                <td>**********</td>
              </tr>
              <tr>
                <td>verifyingContract</td>
                <td>TFFAMLQZybALab4uxHA9RBE7pxhUAjfF3U</td>
                <td>THQGufzL87ZqhkxgYEryRAd7gqFqL5rdc</td>
              </tr>
            </tbody>
          </table>
          <div className={styles.docTableCaption}>Table 1. GasFree Transfer Authorization Parameters</div>

          <h3 id="sig-construct" className={styles.docSubTitle}>
            3.2 Authorization Construction and Signature
          </h3>
          <div className={styles.docFieldDesc}>
            <strong>General structure</strong>
          </div>
          <ul className={styles.docList}>
            <li>
              <strong>MessageDomain:</strong>
            </li>
          </ul>
          <pre className={styles.docCodeBlock}>
            <code>{`const Permit712MessageDomain = {
  name: 'GasFreeController',
  version: 'V1.0.0',
  chainId: ********** // tronWeb.toDecimal('0xcd8690dc'),
  verifyingContract: 'THQGufzL87ZqhkxgYEryRAd7gqFqL5rdc'
}`}</code>
          </pre>
          <div className={styles.docFieldDesc}>Field description:</div>
          <ul className={styles.docList}>
            <li>name: 'GasFreeController', fixed value</li>
            <li>version: 'V1.0.0', fixed value</li>
            <li>chainId: chainId in decimal, please refer to section 3.1</li>
            <li>verifyingContract: GasFreeController contract address, please refer to section 3.1</li>
          </ul>
          <ul className={styles.docList}>
            <li>
              <strong>MessageTypes: fixed value</strong>
            </li>
          </ul>
          <pre className={styles.docCodeBlock}>
            <code>{`const Permit712MessageTypes = {
  PermitTransfer: [
    { name: 'token', type: 'address' },
    { name: 'serviceProvider', type: 'address' },
    { name: 'user', type: 'address' },
    { name: 'receiver', type: 'address' },
    { name: 'value', type: 'uint256' },
    { name: 'maxFee', type: 'uint256' },
    { name: 'deadline', type: 'uint256' },
    { name: 'version', type: 'uint256' },
    { name: 'nonce', type: 'uint256' },
  ]
};`}</code>
          </pre>
          <ul className={styles.docList}>
            <li>
              <strong>Message body:</strong>
            </li>
          </ul>
          <pre className={styles.docCodeBlock}>
            <code>{`const message = {
  token: 'TYX7opR2dj2DXRbtGd1lxx2l3K5dVkxAb8',
  serviceProvider: 'TKWbdfzBq5S9VtsRykbR9bSmxEhBms3Je',
  user: 'THvWiqevPGRWbUaRnk2ouP3Jd9yGC0uCq',
  receiver: 'TMDKn2uDawZfEwt6MiFVtYhmEK6Njskj1',
  value: '9000000',
  maxFee: '2000000',
  deadline: '**********',
  version: 1,
  nonce: 0
};`}</code>
          </pre>
          <div className={styles.docFieldDesc}>Field description:</div>
          <ul className={styles.docList}>
            <li>token: address of transferring token</li>
            <li>serviceProvider: address of Service-Provider</li>
            <li>user: user's EOA address, not GasFree address</li>
            <li>receiver: recipient address of the transfer</li>
            <li>value: amount of the transfer, like 90 USDT equals 90 * 10^6</li>
            <li>
              maxFee: maximum fee limit (transfer fee + activation fee), the smallest unit; for example, 20 USDT equals
              20 * 10^6
            </li>
            <li>deadline: expiration timestamp of the transfer, the unit is seconds; for example, Date.now() / 1000</li>
            <li>version: version of the signature, the current version is 1</li>
            <li>nonce: nonce value of the transfer authorization; for example, 0</li>
          </ul>

          <div className={styles.docFieldDesc}>
            <strong>Sign with Wallet</strong>
          </div>
          <div className={styles.docSectionDesc}>
            Using the above structure, you can call the TRON wallet to sign the TIP-712 Message, which will provide the
            required <code className={styles.docInlineCode}>sig</code> parameter.
          </div>
          <div className={styles.docFieldDesc}>
            <strong>TronLink example:</strong>
          </div>
          <pre className={styles.docCodeBlock}>
            <code>{`const signature = await window.tron.tronWeb.trx._signTypedData(
  Permit712MessageDomain,
  Permit712MessageTypes,
  message
); // remove 0x`}</code>
          </pre>

          <h3 id="sig-endpoint" className={styles.docSubTitle}>
            3.3 Endpoint
          </h3>
          <div className={styles.docSectionDesc}>
            Wallets/users can interact with the Provider through the following endpoint, including submitting GasFree
            transfer authorizations, querying subsequent statuses, etc. They can also withdraw tokens that are not yet
            supported by the Providers to the user's EOA (Externally Owned Account) address.
          </div>
          <table className={styles.docTable}>
            <thead>
              <tr>
                <th>Services</th>
                <th>TRON - Mainnet</th>
                <th>TRON - Nile testnet</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>provider-#1</td>
                <td>
                  <a href="https://open.gasfree.io/tron/" target="_blank" rel="noopener noreferrer">
                    https://open.gasfree.io/tron/
                  </a>
                </td>
                <td>
                  <a href="https://open-test.gasfree.io/nile/" target="_blank" rel="noopener noreferrer">
                    https://open-test.gasfree.io/nile/
                  </a>
                </td>
              </tr>
              <tr>
                <td>GasFree website</td>
                <td>
                  <a href="https://gasfree.io" target="_blank" rel="noopener noreferrer">
                    https://gasfree.io
                  </a>
                </td>
                <td>
                  <a href="https://test.gasfree.io" target="_blank" rel="noopener noreferrer">
                    https://test.gasfree.io
                  </a>
                </td>
              </tr>
              <tr>
                <td>Assets Withdrawal</td>
                <td>
                  <a href="https://gasfree.io/withdraw" target="_blank" rel="noopener noreferrer">
                    https://gasfree.io/withdraw
                  </a>
                </td>
                <td>
                  <a href="https://test.gasfree.io/withdraw" target="_blank" rel="noopener noreferrer">
                    https://test.gasfree.io/withdraw
                  </a>
                </td>
              </tr>
            </tbody>
          </table>
          <div className={styles.docTableCaption}>Table 2. Endpoints of the Services</div>
          <div className={styles.docSectionNote}>
            Note: Currently, GasFree only provides services in the TRON network and can be extended to Ethereum and
            various EVM-compatible chains in the future.
          </div>

          <h3 id="sig-faucet" className={styles.docSubTitle}>
            3.4 Faucet
          </h3>
          <div className={styles.docSectionDesc}>
            Nile testnet faucet:{' '}
            <a href="https://nileex.io/join/getJoinPage" target="_blank" rel="noopener noreferrer">
              https://nileex.io/join/getJoinPage
            </a>
            ; developers can obtain TRX and USDT at this page for test purposes.
          </div>

          <h2 id="notes" className={styles.docSectionTitle}>
            4. Notes
          </h2>
          <div style={{ marginBottom: 0 }}>
            <strong className={styles.docStrongRed}>
              1. It is not recommended that the testnet environment be provided to users.
            </strong>
            <p>
              <strong>
                The GasFree environment on the TRON testnet is only available for developer teams to conduct testing and
                debugging during integration.
              </strong>
            </p>
            <p>
              <strong>
                It is strongly recommended to close the entrance to the GasFree test environment for ordinary users
                after the official launch to prevent users from filling in the wrong recipient addresses and incurring
                asset losses.
              </strong>
            </p>
            <p>
              <strong>
                2. It is recommended to conduct balance and status verification before submitting transfer
                authorization. For details, please refer to the definition of{' '}
                <a href="#" className={styles.docLink}>
                  GasFree account interface
                </a>
                .
              </strong>
            </p>
            <p>
              <strong>
                Unless otherwise specified, all references to asset balances and amounts in this document are in the
                smallest unit.
              </strong>
            </p>
          </div>

          <h2 id="apis" className={styles.docSectionTitle}>
            5. APIs
          </h2>
          <h3 id="api-auth" className={styles.docSubTitle}>
            API Authentication
          </h3>
          <div className={styles.docSectionDesc}>
            After applying for access to the GasFree project, the access party will receive a pair of keys:{' '}
            <strong>API Key</strong> and <strong>API Secret</strong>. This key pair is used to sign API requests. Please
            make sure to keep the API Secret properly. The GasFree server verifies the requests, and the signature
            information is transmitted through the HTTP header.
            <br />
            Please submit your <strong>API Key</strong> and <strong>API Secret</strong> applications{' '}
            <a href="#" className={styles.docLink}>
              here
            </a>
            .
          </div>
          <div className={styles.docFieldDesc}>
            <strong>HTTP header definition:</strong>
          </div>
          <pre className={styles.docCodeBlock}>
            <code>{`{
  "timestamp": 1731912286, // unit: second
  "Authorization": "ApiKey {api_key}:{signature}"
}`}</code>
          </pre>
          <div className={styles.docFieldDesc}>
            <strong>API Signature and Authentication:</strong>
          </div>
          <ol className={styles.docSectionList}>
            <li style={{ listStyle: 'decimal' }}>
              Construct the string to be signed: including the request method, request path, and timestamp.
            </li>
            <li style={{ listStyle: 'decimal' }}>
              Calculate the signature: Use the HMAC-SHA256 algorithm and the API secret to perform a hash operation on
              the string to be signed, and then perform base64 encoding on the result.
            </li>
            <li style={{ listStyle: 'decimal' }}>Add the signature to the request header.</li>
          </ol>
          <div className={styles.docFieldDesc}>
            <strong>Example:</strong>
          </div>
          <pre className={styles.docCodeBlock}>
            <code>{`import hmac
import hashlib
import base64
import time
import requests

API_KEY = 'YOUR_API_KEY'
API_SECRET = 'YOUR_API_SECRET'

method = 'GET'
path = '/api/v1/config/token/all'
timestamp = int(time.time())
message = f"{method}{path}{timestamp}"
signature = base64.b64encode(
    hmac.new(
        API_SECRET.encode('utf-8'),
        message.encode('utf-8'),
        hashlib.sha256
    ).digest()
).decode('utf-8')

url = 'https://test-nile.gasfree.io' + path
headers = {
    'timestamp': f'{timestamp}',
    'Authorization': f'ApiKey {API_KEY}:{signature}'
}

response = requests.get(url, headers=headers)
print(response.json())`}</code>
          </pre>

          <h3 id="api-format" className={styles.docSubTitle}>
            API Format Definition
          </h3>
          <div className={styles.docSectionDesc}>
            When the service runs normally, the HTTP code returned by the API is `200`. Parameter errors or runtime
            errors are reflected in the HTTP body.
          </div>
          <div className={styles.docFieldDesc}>
            <strong>HTTP body definition:</strong>
          </div>
          <pre className={styles.docCodeBlock}>
            <code>{`{
  "code": 200, // indicates if request returns normally, 200 means normal, 400 indicates failure due to input error, and 500 indicates failure due to runtime error
  "reason": null, // exception name
  "message": "", // exception info, usually a combined string
  "data": "" // return data of the API interface; otherwise, it is null
}`}</code>
          </pre>
          <div className={styles.docFieldDesc}>
            <strong>Error return example:</strong>
          </div>
          <pre className={styles.docCodeBlock}>
            <code>{`{
  "code": 400,
  "reason": "GasFreeAddressNotFoundException",
  "message": "123",
  "data": null
}`}</code>
          </pre>
          <div className={styles.docFieldDesc}>
            <strong>Successful return example:</strong>
          </div>
          <pre className={styles.docCodeBlock}>
            <code>{`{
  "code": 200,
  "reason": null,
  "message": null,
  "data": {
    "tokens": [
      {
        "tokenAddress": "TYXo9YRdj2D9XRtb6411X22J3K5VJxAeBE",
        "createdAt": "2024-09-17T09:46:24.801+00:00",
        "updatedAt": "2024-09-11T06:57:10.244+00:00",
        "activateFee": 1000000,
        "transferFee": 1000000,
        "supported": true,
        "symbol": "USDT",
        "decimal": 6
      }
    ]
  }
}`}</code>
          </pre>

          <h3 id="api-token-all" className={styles.docSubTitle}>
            GET /api/v1/config/token/all
          </h3>
          <div className={styles.docSectionDesc}>
            The complete request URL for the services provided by Provider #1 on the TRON mainnet is:{' '}
            <a href="https://open.gasfree.io/tron/api/v1/config/token/all" target="_blank" rel="noopener noreferrer">
              https://open.gasfree.io/tron/api/v1/config/token/all
            </a>
          </div>
          <div className={styles.docSectionDesc}>Get the contract list of all supported tokens.</div>
          <ul className={styles.docList}>
            <li>
              <strong>Request parameters:</strong> None
            </li>
            <li>
              <strong>Return:</strong> <code>tokens</code>, contain the contract list of all supported tokens.
              <br />
              The field description of the token structure:
              <ul>
                <li>
                  <code>tokenAddress</code>: token contract address
                </li>
                <li>
                  <code>activateFee</code>: the activation handling fee paid in the transfer token, is measured in the
                  smallest unit of that token; this value can be adjusted according to actual circumstances later.
                </li>
                <li>
                  <code>transferFee</code>: the transfer fee, paid in the transfer currency, is measured in the smallest
                  unit of that token; this value can be adjusted according to actual circumstances later.
                </li>
                <li>
                  <code>symbol</code>: token name
                </li>
                <li>
                  <code>decimal</code>: token precision
                </li>
              </ul>
            </li>
          </ul>
          <div className={styles.docFieldDesc}>
            <strong>Return example:</strong>
          </div>
          <pre className={styles.docCodeBlock}>
            <code>{`{
  "code": 200,
  "reason": null,
  "message": null,
  "data": {
    "tokens": [
      {
        "tokenAddress": "TYXo9YRdj2D9XRtb6411X22J3K5VJxAeBE",
        "createdAt": "2024-10-09T08:14:12.560+00:00",
        "updatedAt": "2024-10-09T08:14:12.560+00:00",
        "activateFee": 10000000, // stands for 10 USDT
        "transferFee": 10000000, // stands for 10 USDT
        "supported": true,
        "symbol": "USDT",
        "decimal": 6
      }
    ]
  }
}`}</code>
          </pre>

          <h3 id="api-provider-all" className={styles.docSubTitle}>
            GET /api/v1/config/provider/all
          </h3>
          <div className={styles.docSectionDesc}>Get the list of all supported service providers.</div>
          <ul className={styles.docList}>
            <li>
              <strong>Request parameters:</strong> None
            </li>
            <li>
              <strong>Return:</strong> <code>providers</code>, contains the list of all available Service-Providers.
              <br />
              The field description of the provider structure:
              <ul>
                <li>
                  <code>address</code>: address of the Service-Provider
                </li>
                <li>
                  <code>name</code>: Provider's name
                </li>
                <li>
                  <code>icon</code>: Provider's icon
                </li>
                <li>
                  <code>website</code>: Provider's website
                </li>
                <li>
                  <code>config</code>: system parameters of the Provider
                  <ul>
                    <li>
                      <strong>maxPendingTransfer</strong>: the maximum number of transfer authorizations waiting to be
                      on chain that users are allowed to submit; if the number exceeds this limit, an error will be
                      reported upon submission, and users must wait until the previous transfer authorizations are
                      successfully on chain before they can continue to send new ones. (Currently, only one transfer
                      authorization in the "pending" status is supported for the same account.)
                    </li>
                    <li>
                      <strong>minDeadlineDuration</strong>: the minimum deadline interval; transfer authorizations with
                      a deadline less than this value from the current time will be rejected; the unit is seconds.
                    </li>
                    <li>
                      <strong>maxDeadlineDuration</strong>: the maximum deadline interval; transfer authorizations with
                      a deadline more than this value from the current time will be rejected; the unit is seconds.
                    </li>
                    <li>
                      <strong>defaultDeadlineDuration</strong>: default deadline interval, recommended.
                    </li>
                  </ul>
                </li>
              </ul>
            </li>
          </ul>
          <div className={styles.docFieldDesc}>
            <strong>Return example:</strong>
          </div>
          <pre className={styles.docCodeBlock}>
            <code>{`{
  "code": 200,
  "reason": null,
  "message": null,
  "data": {
    "providers": [
      {
        "address": "TQ6qStrS2ZJ96jcieJC8AutTxwqJEtmjfp",
        "name": "Provider-1",
        "icon": "",
        "website": "",
        "config": {
          "maxPendingTransfer": 1,
          "minDeadlineDuration": 60,
          "maxDeadlineDuration": 600,
          "defaultDeadlineDuration": 180
        }
      }
    ]
  }
}`}</code>
          </pre>

          <h3 id="api-address" className={styles.docSubTitle}>
            GET /api/v1/address/&#123;accountAddress&#125;
          </h3>
          <div className={styles.docSectionDesc}>
            Query the relevant information of the user's GasFree account, including whether it is activated, the nonce
            value, the supported tokens, and assets.
          </div>
          <ol className={styles.docSectionList}>
            <li>
              <strong>
                Query the latest assets of the GasFree account on-chain. Combine the “frozen” value provided by this
                interface to limit the maximum transfer-out amount of users. Otherwise, it may lead to submission
                failures or transfer failures.
              </strong>
            </li>
          </ol>
          <ul className={styles.docList}>
            <li>
              <strong>Request parameters:</strong> <code>accountAddress</code> is the user's EOA address.
            </li>
            <li>
              <strong>Return:</strong> asset info of the user's GasFree account.
              <br />
              Field description:
              <ul>
                <li>
                  <code>accountAddress</code>: user's EOA address
                </li>
                <li>
                  <code>gasFreeAddress</code>: user's GasFree account address
                </li>
                <li>
                  <code>active</code>: activation status of the GasFree account
                </li>
                <li>
                  <code>nonce</code>: the recommended nonce value for the next GasFree Transfer; since there may be
                  transfer authorization waiting to be on-chain, the nonce on the chain may not necessarily be
                  applicable; the back end will provide the recommended nonce value by comprehensively considering the
                  situations on the chain and in the queue.
                </li>
                <li>
                  <code>allowSubmit</code>:{' '}
                  <strong>whether users are currently allowed to continue submitting transfer authorization.</strong>
                </li>
                <li>
                  <code>assets</code>: details of the assets that users are processing through the GasFree service
                  provider.
                  <ul>
                    <li>
                      <code>tokenAddress</code>: contract address of the token
                    </li>
                    <li>
                      <code>tokenSymbol</code>: token name
                    </li>
                    <li>
                      <code>activateFee</code>: the activation handling fee for this token, is measured in the smallest
                      unit of this token.
                    </li>
                    <li>
                      <code>transferFee</code>: the transfer fee for this token, is measured in the smallest unit of
                      this token.
                    </li>
                    <li>
                      <code>decimal</code>: precision of the token
                    </li>
                    <li>
                      <code>frozen</code>: current amount of the GasFree transfer in progress, including the fees
                    </li>
                  </ul>
                </li>
              </ul>
            </li>
          </ul>
          <div className={styles.docFieldDesc}>
            <strong>Return example:</strong>
          </div>
          <pre className={styles.docCodeBlock}>
            <code>{`{
  "code": 200,
  "reason": null,
  "message": null,
  "data": {
    "accountAddress": "THKbWd2g5aS9tY59xk8hp5xMnbE8m3B3E",
    "gasFreeAddress": "TLCvf7MktLG7XkbJRyUwnvCeDnaEXYkcbC",
    "active": true,
    "nonce": 1,
    "allowSubmit": false,
    "assets": [
      {
        "tokenAddress": "TYXo9YRdj2D9XRtb6411X22J3K5VJxAeBE",
        "tokenSymbol": "USDT",
        "activateFee": 1000000,
        "transferFee": 1000000,
        "decimal": 6,
        "frozen": 0
      }
    ]
  }
}`}</code>
          </pre>

          <h3 id="api-submit" className={styles.docSubTitle}>
            POST /api/v1/gasfree/submit
          </h3>
          <div className={styles.docSectionDesc}>Initiate a GasFree transfer authorization.</div>
          <ul className={styles.docList}>
            <li>
              <strong>Request parameters:</strong>
              <ul>
                <li>token: address of the token for transfer</li>
                <li>serviceProvider: address of the service-provider</li>
                <li>user: user's EOA address, not the GasFree address</li>
                <li>receiver: recipient address of the transfer</li>
                <li>value: amount for the transfer</li>
                <li>maxFee: the maximum fee limit (transfer fee + activation fee)</li>
                <li>deadline: the expiration timestamp of this transfer, in seconds</li>
                <li>version: signature version of transfer authorization</li>
                <li>nonce: nonce value of this transfer authorization</li>
                <li>sig: user's signature of the GasFree transfer authorization</li>
              </ul>
            </li>
          </ul>
          <div className={styles.docFieldDesc}>
            <strong>Request Example:</strong>
          </div>
          <pre className={styles.docCodeBlock}>
            <code>{`{
  "token": "TYXo9YRdj2D9XRtb6411X22J3K5VJxAeBE",
  "serviceProvider": "TCTnR3aBdAkdA0XY7CxxEJtrQw6pWj8nPT",
  "user": "THKbWd2g5aS9tY59xk8hp5xMnbE8m3B3E",
  "receiver": "TKj3ndEwWFLyAFwF2TmydJ8R2RLfkaeucF",
  "value": 100000,
  "maxFee": 1000000,
  "deadline": **********,
  "version": 1,
  "nonce": 9,
  "sig": "9d463be03af56bc93fa619e2e1e743f65b5c0d94b9bd340a94e27f66da6413618ecc841b9d516fa739928d8047daa0badabede2f92814e67a8b99e4fd9877341b"
}`}</code>
          </pre>
          <ul className={styles.docList}>
            <li>
              <strong>Return:</strong> basic information of this GasFree transfer authorization.
              <br />
              Field description:
              <ul>
                <li>id: traceId of GasFree transfer authorization, not the transactionId</li>
                <li>accountAddress: user's EOA address</li>
                <li>gasFreeAddress: user's GasFree account address</li>
                <li>providerAddress: address of the service-provider</li>
                <li>targetAddress: recipient address</li>
                <li>tokenAddress: contract address of the transferred token</li>
                <li>amount: amount of the transfer</li>
                <li>maxFee: limit of maximum fee</li>
                <li>signature: user's signature</li>
                <li>version: signature version of transfer authorization</li>
                <li>nonce: nonce value specified for the transfer</li>
                <li>expiredAt: expiration time of this transfer</li>
                <li>
                  state: current state of this transfer, with the following values:
                  <ul>
                    <li>WAITING, // Not started</li>
                    <li>INPROGRESS, // In progress</li>
                    <li>CONFIRMING, // Confirming</li>
                    <li>SUCCEED, // Successful</li>
                    <li>FAILED, // Failed</li>
                  </ul>
                </li>
                <li>estimatedActivateFee: estimated activation fee of the address</li>
                <li>estimatedTransferFee: estimated transfer fee</li>
              </ul>
            </li>
          </ul>
          <div className={styles.docFieldDesc}>
            <strong>Return example:</strong>
          </div>
          <pre className={styles.docCodeBlock}>
            <code>{`{
  "code": 200,
  "reason": null,
  "message": null,
  "data": {
    "id": "1234567890abcdef",
    "accountAddress": "THKbWd2g5aS9tY59xk8hp5xMnbE8m3B3E",
    "gasFreeAddress": "TLCvf7MktLG7XkbJRyUwnvCeDnaEXYkcbC",
    "providerAddress": "TCTnR3aBdAkdA0XY7CxxEJtrQw6pWj8nPT",
    "targetAddress": "TKj3ndEwWFLyAFwF2TmydJ8R2RLfkaeucF",
    "tokenAddress": "TYXo9YRdj2D9XRtb6411X22J3K5VJxAeBE",
    "amount": 100000,
    "maxFee": 1000000,
    "signature": "9d463be03af56bc93fa619e2e1e743f65b5c0d94b9bd340a94e27f66da6413618ecc841b9d516fa739928d8047daa0badabede2f92814e67a8b99e4fd9877341b",
    "version": 1,
    "nonce": 9,
    "expiredAt": **********,
    "state": "WAITING",
    "estimatedActivateFee": 100,
    "estimatedTransferFee": 100
  }
}`}</code>
          </pre>
          <div className={styles.docFieldDesc}>
            <strong>Error example:</strong>
          </div>
          <pre className={styles.docCodeBlock}>
            <code>{`{
  "code": 401,
  "reason": "GasFreeAddressNotEnough",
  "message": "Insufficient balance",
  "data": null
}`}</code>
          </pre>
          <ul className={styles.docList}>
            <li>
              <strong>Error types:</strong>
              <ul>
                <li>ProviderAddressNotMatchException: Provider's address does not match</li>
                <li>GasFreeDeadlineExpiredException: authorization is expired</li>
                <li>InvalidSignatureException: signature is invalid.</li>
                <li>TokenNotPendingTransferException: token transferred in not supported.</li>
                <li>TooManyPendingTransferException: there are too many pending transfer authorizations.</li>
                <li>VersionNotSupportedException: signature version of transfer authorization is not supported.</li>
                <li>NonceNotMatchException: nonce does not match.</li>
                <li>MaxFeeExceedException: maxFee used exceeds the limit of the maximum fee.</li>
                <li>InsufficientBalanceException: insufficient balance</li>
              </ul>
            </li>
          </ul>

          <h3 id="api-trace" className={styles.docSubTitle}>
            GET /api/v1/gasfree/&#123;traceId&#125;
          </h3>
          <div className={styles.docSectionDesc}>
            Query the details of a specified GasFree transfer authorization.{' '}
            <strong>
              All fields related to the transaction will be empty if the query is made either before the transfer
              authorization is submitted to the chain or the verification before submitting fails.
            </strong>
          </div>
          <ul className={styles.docList}>
            <li>
              <strong>Required parameters:</strong>
              <ul>
                <li>traceId: id of the GasFree transfer authorization; a path parameter.</li>
              </ul>
            </li>
            <li>
              <strong>Return:</strong> detailed info on the GasFree transfer authorization.
              <br />
              <span style={{ fontFamily: 'monospace' }}>Field description:</span>
              <ul>
                <li>id: traceId of the GasFree transfer authorization recorded, not the transactionId.</li>
                <li>createdAt: creation time of the transfer authorization.</li>
                <li>accountAddress: user's EOA address.</li>
                <li>gasFreeAddress: user's GasFree account address.</li>
                <li>providerAddress: service-provider address.</li>
                <li>targetAddress: recipient address.</li>
                <li>nonce: nonce value of the GasFree transfer authorization.</li>
                <li>tokenAddress: contract address of the token transferred.</li>
                <li>amount: amount actually transferred to the recipient's address.</li>
                <li>expiredAt: expiration time of this transfer.</li>
                <li>
                  state: current state of this transfer, with the following values:
                  <ul>
                    <li>WAITING, // Not started</li>
                    <li>INPROGRESS, // In progress</li>
                    <li>CONFIRMING, // Confirming</li>
                    <li>SUCCEED, // Successful</li>
                    <li>FAILED, // Failed</li>
                  </ul>
                </li>
                <li>estimatedActivateFee: estimated activation fee.</li>
                <li>estimatedTransferFee: estimated transfer fee.</li>
                <li>estimatedTotalFee: estimated activation fee pulse estimated transfer fee.</li>
                <li>
                  estimatedTotalCost: estimated amount that the user needs to pay, including the total fee and the
                  transfer amount.
                </li>
                <li>txnHash: transactionId of the corresponding transaction on-chain.</li>
                <li>txnBlockNum: block height of the corresponding transaction on-chain.</li>
                <li>
                  txnBlockTimestamp: timestamp of the block contains the corresponding transaction, in milliseconds.
                </li>
                <li>
                  txnState: state of the corresponding on-chain transaction, with the following values:
                  <ul>
                    <li>INIT, // initial state</li>
                    <li>NOT_ON_CHAIN, // Not on-chain</li>
                    <li>ON_CHAIN, // On-chain, not solidified</li>
                    <li>SOLIDITY, // Solidified</li>
                    <li>ON_CHAIN_FAILED, // failed to be on-chain</li>
                  </ul>
                </li>
                <li>txnActivateFee: actual activation fee consumed.</li>
                <li>txnTransferFee: actual transfer fee consumed.</li>
                <li>txnTotalFee: actual total fee consumed.</li>
                <li>txnAmount: actual transferred amount to the recipient address</li>
                <li>
                  txnTotalCost: actual amount paid by the user, including the total fee and the transferred amount.
                </li>
              </ul>
            </li>
          </ul>
        </main>
      </div>
    </div>
  );
}
