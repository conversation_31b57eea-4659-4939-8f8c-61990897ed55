<!doctype html>
<html>
<head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="../logo.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>GasFree Developer Documentation</title>

    <link href='https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap' rel='stylesheet'>
    <style type='text/css'>
        :root {
            --primary-color: #1677ff;
            --text-color: #333333;
            --bg-color: #ffffff;
            --sidebar-bg: #f5f7f9;
            --code-bg: #f6f8fa;
            --border-color: #e5e7eb;
            --heading-color: #111827;
            --link-color: #1677ff;
            --monospace: "SF Mono", Monaco, Consolas, "Courier New", monospace;
        }
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            font-size: 16px;
            overflow-x: initial !important;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            color: var(--text-color);
            line-height: 1.6;
            background: var(--bg-color);
            display: flex;
            min-height: 100vh;
            -webkit-font-smoothing: antialiased;
        }
        /* 侧边栏样式 */
        .sidebar {
            width: 280px;
            background: var(--sidebar-bg);
            padding: 2rem 1rem;
            border-right: 1px solid var(--border-color);
            position: fixed;
            height: 100vh;
            overflow-y: auto;
        }

        .sidebar ul {
            list-style: none;
        }

        .sidebar li {
            margin: 0.5rem 0;
        }

        .sidebar a {
            color: var(--text-color);
            text-decoration: none;
            font-size: 0.95rem;
            display: block;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            transition: all 0.2s;
        }

        .sidebar a:hover {
            background: rgba(0, 0, 0, 0.05);
            color: var(--primary-color);
        }

        /* 主内容区域样式 */
        #write {
            margin: 0 auto;
            height: auto;
            width: inherit;
            max-width: 960px;
            word-break: normal;
            word-wrap: break-word;
            position: relative;
            white-space: normal;
            overflow-x: visible;
            padding: 2rem 3rem;
            margin-left: 280px;
            background: var(--bg-color);
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
        }
        /* 标题样式 */
        h1, h2, h3, h4, h5, h6 {
            font-weight: 600;
            line-height: 1.25;
            margin: 2rem 0 1rem;
            color: var(--heading-color);
        }

        h1 {
            font-size: 2.5rem;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 0.5rem;
        }

        h2 { font-size: 1.75rem; }
        h3 { font-size: 1.5rem; }
        h4 { font-size: 1.25rem; }
        h5 { font-size: 1.125rem; }
        h6 { font-size: 1rem; }

        /* 段落和列表样式 */
        p {
            margin: 1rem 0;
            line-height: 1.8;
        }

        ul, ol {
            padding-left: 1.5rem;
            margin: 1rem 0;
        }

        li {
            margin: 0.5rem 0;
        }
        /* 响应式设计 */
        @media screen and (max-width: 768px) {
            html {
                font-size: 14px;
            }

            .sidebar {
                display: none;
            }

            #write {
                margin-left: 0;
                padding: 1rem;
                max-width: 100%;
            }

            pre, .CodeMirror {
                margin: 1rem -1rem;
                border-radius: 0;
            }

            table {
                display: block;
                overflow-x: auto;
                white-space: nowrap;
            }
        }

        /* 打印样式 */
        @media print {
            html {
                font-size: 12px;
            }

            .sidebar {
                display: none;
            }

            #write {
                margin: 0;
                padding: 0;
                max-width: 100%;
            }

            pre, code {
                border: 1px solid #ddd;
                page-break-inside: avoid;
            }

            table {
                page-break-inside: avoid;
            }

            h1, h2, h3 {
                page-break-after: avoid;
            }

            img {
                max-width: 100% !important;
            }
        }
        /* 代码块样式 */
        pre, code {
            font-family: var(--monospace);
            background: var(--code-bg);
            border-radius: 6px;
            font-size: 0.9em;
            line-height: 1.5;
        }

        pre {
            padding: 1rem;
            margin: 1.5rem 0;
            overflow-x: auto;
            border: 1px solid var(--border-color);
        }

        pre code {
            background: none;
            padding: 0;
            border-radius: 0;
        }

        .CodeMirror {
            height: auto !important;
            background: var(--code-bg) !important;
            border-radius: 6px;
            border: 1px solid var(--border-color);
            font-family: var(--monospace);
        }

        .CodeMirror-lines {
            padding: 1rem 0;
        }

        .CodeMirror-line {
            padding: 0 1rem !important;
        }

        /* 表格样式 */
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 1.5rem 0;
            background: white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        th, td {
            padding: 0.75rem 1rem;
            border: 1px solid var(--border-color);
            text-align: left;
        }

        th {
            background: var(--sidebar-bg);
            font-weight: 600;
            color: var(--heading-color);
        }

        tr:nth-child(even) {
            background: var(--sidebar-bg);
        }

        tr:hover {
            background: rgba(0, 0, 0, 0.02);
        }
.CodeMirror-gutters { border-right-width: 0px; background-color: inherit; }
.CodeMirror-linenumber { -webkit-user-select: none; }
.CodeMirror { text-align: left; }
.CodeMirror-placeholder { opacity: 0.3; }
.CodeMirror pre { padding: 0px 4px; }
.CodeMirror-lines { padding: 0px; }
div.hr:focus { cursor: none; }
#write pre { white-space: pre-wrap; }
#write.fences-no-line-wrapping pre { white-space: pre; }
#write pre.ty-contain-cm { white-space: normal; }
.CodeMirror-gutters { margin-right: 4px; }
.md-fences { font-size: 0.9rem; display: block; break-inside: avoid; text-align: left; overflow: visible; white-space: pre; background-image: inherit; background-size: inherit; background-attachment: inherit; background-origin: inherit; background-clip: inherit; background-color: inherit; position: relative !important; background-position: inherit; background-repeat: inherit; }
.md-fences-adv-panel { width: 100%; margin-top: 10px; text-align: center; padding-top: 0px; padding-bottom: 8px; overflow-x: auto; }
#write .md-fences.mock-cm { white-space: pre-wrap; }
.md-fences.md-fences-with-lineno { padding-left: 0px; }
#write.fences-no-line-wrapping .md-fences.mock-cm { white-space: pre; overflow-x: auto; }
.md-fences.mock-cm.md-fences-with-lineno { padding-left: 8px; }
.CodeMirror-line, twitterwidget { break-inside: avoid; }
svg { break-inside: avoid; }
.footnotes { opacity: 0.8; font-size: 0.9rem; margin-top: 1em; margin-bottom: 1em; }
.footnotes + .footnotes { margin-top: 0px; }
.md-reset { margin: 0px; padding: 0px; border: 0px; outline: 0px; vertical-align: top; text-decoration: none; text-shadow: none; float: none; position: static; width: auto; height: auto; white-space: nowrap; cursor: inherit; line-height: normal; font-weight: 400; text-align: left; box-sizing: content-box; direction: ltr; background-position: 0px 0px; }
li div { padding-top: 0px; }
blockquote { margin: 1rem 0px; }
li .mathjax-block, li p { margin: 0.5rem 0px; }
li blockquote { margin: 1rem 0px; }
li { margin: 0px; position: relative; }
blockquote > :last-child { margin-bottom: 0px; }
blockquote > :first-child, li > :first-child { margin-top: 0px; }
.footnotes-area { color: rgb(136, 136, 136); margin-top: 0.714rem; padding-bottom: 0.143rem; white-space: normal; }
#write .footnote-line { white-space: pre-wrap; }
@media print {
  body, html { border: 1px solid transparent; height: 99%; break-after: avoid; break-before: avoid; font-variant-ligatures: no-common-ligatures; }
  #write { margin-top: 0px; border-color: transparent !important; padding-top: 0px !important; padding-bottom: 0px !important; }
  .typora-export * { print-color-adjust: exact; }
  .typora-export #write { break-after: avoid; }
  .typora-export #write::after { height: 0px; }
  .is-mac table { break-inside: avoid; }
  #write > p:nth-child(1) { margin-top: 0px; }
  .typora-export-show-outline .typora-export-sidebar { display: none; }
  figure { overflow-x: visible; }
}
.footnote-line { margin-top: 0.714em; font-size: 0.7em; }
a img, img a { cursor: pointer; }
pre.md-meta-block { font-size: 0.8rem; min-height: 0.8rem; white-space: pre-wrap; background-color: rgb(204, 204, 204); display: block; overflow-x: hidden; }
p > .md-image:only-child:not(.md-img-error) img, p > img:only-child { display: block; margin: auto; }
#write.first-line-indent p > .md-image:only-child:not(.md-img-error) img { left: -2em; position: relative; }
p > .md-image:only-child { display: inline-block; width: 100%; }
#write .MathJax_Display { margin: 0.8em 0px 0px; }
.md-math-block { width: 100%; }
.md-math-block:not(:empty)::after { display: none; }
.MathJax_ref { fill: currentcolor; }
[contenteditable="true"]:active, [contenteditable="true"]:focus, [contenteditable="false"]:active, [contenteditable="false"]:focus { outline: 0px; box-shadow: none; }
.md-task-list-item { position: relative; list-style-type: none; }
.task-list-item.md-task-list-item { padding-left: 0px; }
.md-task-list-item > input { position: absolute; top: 0px; left: 0px; margin-left: -1.2em; margin-top: calc(1em - 10px); border: none; }
.math { font-size: 1rem; }
.md-toc { min-height: 3.58rem; position: relative; font-size: 0.9rem; border-radius: 10px; }
.md-toc-content { position: relative; margin-left: 0px; }
.md-toc-content::after, .md-toc::after { display: none; }
.md-toc-item { display: block; color: rgb(65, 131, 196); }
.md-toc-item a { text-decoration: none; }
.md-toc-inner:hover { text-decoration: underline; }
.md-toc-inner { display: inline-block; cursor: pointer; }
.md-toc-h1 .md-toc-inner { margin-left: 0px; font-weight: 700; }
.md-toc-h2 .md-toc-inner { margin-left: 2em; }
.md-toc-h3 .md-toc-inner { margin-left: 4em; }
.md-toc-h4 .md-toc-inner { margin-left: 6em; }
.md-toc-h5 .md-toc-inner { margin-left: 8em; }
.md-toc-h6 .md-toc-inner { margin-left: 10em; }
@media screen and (max-width: 48em) {
  .md-toc-h3 .md-toc-inner { margin-left: 3.5em; }
  .md-toc-h4 .md-toc-inner { margin-left: 5em; }
  .md-toc-h5 .md-toc-inner { margin-left: 6.5em; }
  .md-toc-h6 .md-toc-inner { margin-left: 8em; }
}
a.md-toc-inner { font-size: inherit; font-style: inherit; font-weight: inherit; line-height: inherit; }
.footnote-line a:not(.reversefootnote) { color: inherit; }
.reversefootnote { font-family: ui-monospace, sans-serif; }
.md-attr { display: none; }
.md-fn-count::after { content: "."; }
code, pre, samp, tt { font-family: var(--monospace); }
kbd { margin: 0px 0.1em; padding: 0.1em 0.6em; font-size: 0.8em; color: rgb(36, 39, 41); background-color: rgb(255, 255, 255); border: 1px solid rgb(173, 179, 185); border-radius: 3px; box-shadow: rgba(12, 13, 14, 0.2) 0px 1px 0px, rgb(255, 255, 255) 0px 0px 0px 2px inset; white-space: nowrap; vertical-align: middle; }
.md-comment { color: rgb(162, 127, 3); opacity: 0.6; font-family: var(--monospace); }
code { text-align: left; }
a.md-print-anchor { white-space: pre !important; border: none !important; display: inline-block !important; position: absolute !important; width: 1px !important; right: 0px !important; outline: 0px !important; text-shadow: initial !important; background-position: 0px 0px !important; }
.os-windows.monocolor-emoji .md-emoji { font-family: "Segoe UI Symbol", sans-serif; }
.md-diagram-panel > svg { max-width: 100%; }
[lang="flow"] svg, [lang="mermaid"] svg { max-width: 100%; height: auto; }
[lang="mermaid"] .node text { font-size: 1rem; }
table tr th { border-bottom-width: 0px; }
video { max-width: 100%; display: block; margin: 0px auto; }
iframe { max-width: 100%; width: 100%; border: none; }
.highlight td, .highlight tr { border: 0px; }
mark { background-color: rgb(255, 255, 0); color: rgb(0, 0, 0); }
.md-html-inline .md-plain, .md-html-inline strong, mark .md-inline-math, mark strong { color: inherit; }
.md-expand mark .md-meta { opacity: 0.3 !important; }
mark .md-meta { color: rgb(0, 0, 0); }
@media print {
  .typora-export h1, .typora-export h2, .typora-export h3, .typora-export h4, .typora-export h5, .typora-export h6 { break-inside: avoid; }
}
.md-diagram-panel .messageText { stroke: none !important; }
.md-diagram-panel .start-state { fill: var(--node-fill); }
.md-diagram-panel .edgeLabel rect { opacity: 1 !important; }
.md-fences.md-fences-math { font-size: 1em; }
.md-fences-advanced:not(.md-focus) { padding: 0px; white-space: nowrap; border: 0px; }
.md-fences-advanced:not(.md-focus) { background-image: inherit; background-size: inherit; background-attachment: inherit; background-origin: inherit; background-clip: inherit; background-color: inherit; background-position: inherit; background-repeat: inherit; }
.typora-export-show-outline .typora-export-content { max-width: 1440px; margin: auto; display: flex; flex-direction: row; }
.typora-export-sidebar { width: 300px; font-size: 0.8rem; margin-top: 80px; margin-right: 18px; }
.typora-export-show-outline #write { --webkit-flex: 2; flex: 2 1 0%; }
.typora-export-sidebar .outline-content { position: fixed; top: 0px; max-height: 100%; overflow: hidden auto; padding-bottom: 30px; padding-top: 60px; width: 300px; }
@media screen and (max-width: 1024px) {
  .typora-export-sidebar, .typora-export-sidebar .outline-content { width: 240px; }
}
@media screen and (max-width: 800px) {
  .typora-export-sidebar { display: none; }
}
.outline-content li, .outline-content ul { margin-left: 0px; margin-right: 0px; padding-left: 0px; padding-right: 0px; list-style: none; overflow-wrap: anywhere; }
.outline-content ul { margin-top: 0px; margin-bottom: 0px; }
.outline-content strong { font-weight: 400; }
.outline-expander { width: 1rem; height: 1.428571429rem; position: relative; display: table-cell; vertical-align: middle; cursor: pointer; padding-left: 4px; }
.outline-expander::before { content: ""; position: relative; font-family: Ionicons; display: inline-block; font-size: 8px; vertical-align: middle; }
.outline-item { padding-top: 3px; padding-bottom: 3px; cursor: pointer; }
.outline-expander:hover::before { content: ""; }
.outline-h1 > .outline-item { padding-left: 0px; }
.outline-h2 > .outline-item { padding-left: 1em; }
.outline-h3 > .outline-item { padding-left: 2em; }
.outline-h4 > .outline-item { padding-left: 3em; }
.outline-h5 > .outline-item { padding-left: 4em; }
.outline-h6 > .outline-item { padding-left: 5em; }
.outline-label { cursor: pointer; display: table-cell; vertical-align: middle; text-decoration: none; color: inherit; }
.outline-label:hover { text-decoration: underline; }
.outline-item:hover { border-color: rgb(245, 245, 245); background-color: var(--item-hover-bg-color); }
.outline-item:hover { margin-left: -28px; margin-right: -28px; border-left-width: 28px; border-left-style: solid; border-left-color: transparent; border-right-width: 28px; border-right-style: solid; border-right-color: transparent; }
.outline-item-single .outline-expander::before, .outline-item-single .outline-expander:hover::before { display: none; }
.outline-item-open > .outline-item > .outline-expander::before { content: ""; }
.outline-children { display: none; }
.info-panel-tab-wrapper { display: none; }
.outline-item-open > .outline-children { display: block; }
.typora-export .outline-item { padding-top: 1px; padding-bottom: 1px; }
.typora-export .outline-item:hover { margin-right: -8px; border-right-width: 8px; border-right-style: solid; border-right-color: transparent; }
.typora-export .outline-expander::before { content: "+"; font-family: inherit; top: -1px; }
.typora-export .outline-expander:hover::before, .typora-export .outline-item-open > .outline-item > .outline-expander::before { content: "−"; }
.typora-export-collapse-outline .outline-children { display: none; }
.typora-export-collapse-outline .outline-item-open > .outline-children, .typora-export-no-collapse-outline .outline-children { display: block; }
.typora-export-no-collapse-outline .outline-expander::before { content: "" !important; }
.typora-export-show-outline .outline-item-active > .outline-item .outline-label { font-weight: 700; }
.md-inline-math-container mjx-container { zoom: 0.95; }
mjx-container { break-inside: avoid; }
.md-alert.md-alert-note { border-left-color: rgb(9, 105, 218); }
.md-alert.md-alert-important { border-left-color: rgb(130, 80, 223); }
.md-alert.md-alert-warning { border-left-color: rgb(154, 103, 0); }
.md-alert.md-alert-tip { border-left-color: rgb(31, 136, 61); }
.md-alert.md-alert-caution { border-left-color: rgb(207, 34, 46); }
.md-alert { padding: 0px 1em; margin-bottom: 16px; color: inherit; border-left-width: 0.25em; border-left-style: solid; border-left-color: rgb(0, 0, 0); }
.md-alert-text-note { color: rgb(9, 105, 218); }
.md-alert-text-important { color: rgb(130, 80, 223); }
.md-alert-text-warning { color: rgb(154, 103, 0); }
.md-alert-text-tip { color: rgb(31, 136, 61); }
.md-alert-text-caution { color: rgb(207, 34, 46); }
.md-alert-text { font-size: 0.9rem; font-weight: 700; }
.md-alert-text svg { fill: currentcolor; position: relative; top: 0.125em; margin-right: 1ch; overflow: visible; }
.md-alert-text-container::after { content: attr(data-text); text-transform: capitalize; pointer-events: none; margin-right: 1ch; }


.CodeMirror { height: auto; }
.CodeMirror.cm-s-inner { background-image: inherit; background-size: inherit; background-attachment: inherit; background-origin: inherit; background-clip: inherit; background-color: inherit; background-position: inherit; background-repeat: inherit; }
.CodeMirror-scroll { overflow: auto hidden; z-index: 3; }
.CodeMirror-gutter-filler, .CodeMirror-scrollbar-filler { background-color: rgb(255, 255, 255); }
.CodeMirror-gutters { border-right-width: 1px; border-right-style: solid; border-right-color: rgb(221, 221, 221); background-image: inherit; background-size: inherit; background-attachment: inherit; background-origin: inherit; background-clip: inherit; background-color: inherit; white-space: nowrap; background-position: inherit; background-repeat: inherit; }
.CodeMirror-linenumber { padding: 0px 3px 0px 5px; text-align: right; color: rgb(153, 153, 153); }
.cm-s-inner .cm-keyword { color: rgb(119, 0, 136); }
.cm-s-inner .cm-atom, .cm-s-inner.cm-atom { color: rgb(34, 17, 153); }
.cm-s-inner .cm-number { color: rgb(17, 102, 68); }
.cm-s-inner .cm-def { color: rgb(0, 0, 255); }
.cm-s-inner .cm-variable { color: rgb(0, 0, 0); }
.cm-s-inner .cm-variable-2 { color: rgb(0, 85, 170); }
.cm-s-inner .cm-variable-3 { color: rgb(0, 136, 85); }
.cm-s-inner .cm-string { color: rgb(170, 17, 17); }
.cm-s-inner .cm-property { color: rgb(0, 0, 0); }
.cm-s-inner .cm-operator { color: rgb(152, 26, 26); }
.cm-s-inner .cm-comment, .cm-s-inner.cm-comment { color: rgb(170, 85, 0); }
.cm-s-inner .cm-string-2 { color: rgb(255, 85, 0); }
.cm-s-inner .cm-meta { color: rgb(85, 85, 85); }
.cm-s-inner .cm-qualifier { color: rgb(85, 85, 85); }
.cm-s-inner .cm-builtin { color: rgb(51, 0, 170); }
.cm-s-inner .cm-bracket { color: rgb(153, 153, 119); }
.cm-s-inner .cm-tag { color: rgb(17, 119, 0); }
.cm-s-inner .cm-attribute { color: rgb(0, 0, 204); }
.cm-s-inner .cm-header, .cm-s-inner.cm-header { color: rgb(0, 0, 255); }
.cm-s-inner .cm-quote, .cm-s-inner.cm-quote { color: rgb(0, 153, 0); }
.cm-s-inner .cm-hr, .cm-s-inner.cm-hr { color: rgb(153, 153, 153); }
.cm-s-inner .cm-link, .cm-s-inner.cm-link { color: rgb(0, 0, 204); }
.cm-negative { color: rgb(221, 68, 68); }
.cm-positive { color: rgb(34, 153, 34); }
.cm-header, .cm-strong { font-weight: 700; }
.cm-del { text-decoration: line-through; }
.cm-em { font-style: italic; }
.cm-link { text-decoration: underline; }
.cm-error { color: red; }
.cm-invalidchar { color: red; }
.cm-constant { color: rgb(38, 139, 210); }
.cm-defined { color: rgb(181, 137, 0); }
div.CodeMirror span.CodeMirror-matchingbracket { color: rgb(0, 255, 0); }
div.CodeMirror span.CodeMirror-nonmatchingbracket { color: rgb(255, 34, 34); }
.cm-s-inner .CodeMirror-activeline-background { background-image: inherit; background-size: inherit; background-attachment: inherit; background-origin: inherit; background-clip: inherit; background-color: inherit; background-position: inherit; background-repeat: inherit; }
.CodeMirror { position: relative; overflow: hidden; }
.CodeMirror-scroll { height: 100%; outline: 0px; position: relative; box-sizing: content-box; background-image: inherit; background-size: inherit; background-attachment: inherit; background-origin: inherit; background-clip: inherit; background-color: inherit; background-position: inherit; background-repeat: inherit; }
.CodeMirror-sizer { position: relative; }
.CodeMirror-gutter-filler, .CodeMirror-hscrollbar, .CodeMirror-scrollbar-filler, .CodeMirror-vscrollbar { position: absolute; z-index: 6; display: none; outline: 0px; }
.CodeMirror-vscrollbar { right: 0px; top: 0px; overflow: hidden; }
.CodeMirror-hscrollbar { bottom: 0px; left: 0px; overflow: auto hidden; }
.CodeMirror-scrollbar-filler { right: 0px; bottom: 0px; }
.CodeMirror-gutter-filler { left: 0px; bottom: 0px; }
.CodeMirror-gutters { position: absolute; left: 0px; top: 0px; padding-bottom: 10px; z-index: 3; overflow-y: hidden; }
.CodeMirror-gutter { white-space: normal; height: 100%; box-sizing: content-box; padding-bottom: 30px; margin-bottom: -32px; display: inline-block; }
.CodeMirror-gutter-wrapper { position: absolute; z-index: 4; border: none !important; background-position: 0px 0px !important; }
.CodeMirror-gutter-background { position: absolute; top: 0px; bottom: 0px; z-index: 4; }
.CodeMirror-gutter-elt { position: absolute; cursor: default; z-index: 4; }
.CodeMirror-lines { cursor: text; }
.CodeMirror pre { border-radius: 0px; border-width: 0px; font-family: inherit; font-size: inherit; margin: 0px; white-space: pre; word-wrap: normal; color: inherit; z-index: 2; position: relative; overflow: visible; background-position: 0px 0px; }
.CodeMirror-wrap pre { word-wrap: break-word; white-space: pre-wrap; word-break: normal; }
.CodeMirror-code pre { border-right-width: 30px; border-right-style: solid; border-right-color: transparent; width: fit-content; }
.CodeMirror-wrap .CodeMirror-code pre { border-right-style: none; width: auto; }
.CodeMirror-linebackground { position: absolute; inset: 0px; z-index: 0; }
.CodeMirror-linewidget { position: relative; z-index: 2; overflow: auto; }
.CodeMirror-wrap .CodeMirror-scroll { overflow-x: hidden; }
.CodeMirror-measure { position: absolute; width: 100%; height: 0px; overflow: hidden; visibility: hidden; }
.CodeMirror-measure pre { position: static; }
.CodeMirror div.CodeMirror-cursor { position: absolute; visibility: hidden; border-right-style: none; width: 0px; }
.CodeMirror div.CodeMirror-cursor { visibility: hidden; }
.CodeMirror-focused div.CodeMirror-cursor { visibility: inherit; }
.cm-searching { background-color: rgba(255, 255, 0, 0.4); }
span.cm-underlined { text-decoration: underline; }
span.cm-strikethrough { text-decoration: line-through; }
.cm-tw-syntaxerror { color: rgb(255, 255, 255); background-color: rgb(153, 0, 0); }
.cm-tw-deleted { text-decoration: line-through; }
.cm-tw-header5 { font-weight: 700; }
.cm-tw-listitem:first-child { padding-left: 10px; }
.cm-tw-box { border-style: solid; border-right-width: 1px; border-bottom-width: 1px; border-left-width: 1px; border-color: inherit; border-top-width: 0px !important; }
.cm-tw-underline { text-decoration: underline; }
@media print {
  .CodeMirror div.CodeMirror-cursor { visibility: hidden; }
}

.table-of-contents{
    position: fixed;
    top:100px;
    left:20px;
    max-width:320px;
    
}

#gasfree-developer-documentation{
    text-align: center;
    height:120px;
    margin:0 auto 80px;
    line-height:120px;
    white-space:unset;
    padding-bottom:0;
}

@media screen and (max-width: 1000px) {
    .table-of-contents{
        position: relative;
        width:80%;
        margin: 0 auto 20px;
        left:0;
        top:0;
    }

    #gasfree-developer-documentation{
        height:140px;
        margin:0 auto 20px;
        line-height:40px;
        white-space:unset;
        text-align: center;
        padding-bottom:20px;
    }
}



:root {
    --side-bar-bg-color: #fafafa;
    --control-text-color: #777;
}

@include-when-export url(https://fonts.googleapis.com/css?family=Open+Sans:400italic,700italic,700,400&subset=latin,latin-ext);

/* open-sans-regular - latin-ext_latin */
  /* open-sans-italic - latin-ext_latin */
    /* open-sans-700 - latin-ext_latin */
    /* open-sans-700italic - latin-ext_latin */
  html {
    font-size: 16px;
    -webkit-font-smoothing: antialiased;
}

body {
    font-family: "Open Sans","Clear Sans", "Helvetica Neue", Helvetica, Arial, 'Segoe UI Emoji', sans-serif;
    color: rgb(51, 51, 51);
    line-height: 1.6;
}

#write {
    max-width: 860px;
  	margin: 0 auto;
  	padding: 30px;
    padding-bottom: 100px;
}

@media only screen and (min-width: 1000px) {
	#write {
		max-width: 700px;
        margin-right:-50px;
	}
}

@media only screen and (min-width: 1200px) {
	#write {
		max-width: 800px;
        margin-right:-50px;
	}
}

@media only screen and (min-width: 1400px) {
	#write {
		max-width: 800px;
        margin:0 auto;
	}
}

@media only screen and (min-width: 1600px) {
	#write {
		max-width: 900px;
        margin:0 auto;
	}
}

@media only screen and (min-width: 1800px) {
	#write {
		max-width: 1200px;
        margin:0 auto;
	}
}

#write > ul:first-child,
#write > ol:first-child{
    margin-top: 30px;
}

a {
    color: #4183C4;
}
h1,
h2,
h3,
h4,
h5,
h6 {
    position: relative;
    margin-top: 1rem;
    margin-bottom: 1rem;
    font-weight: bold;
    line-height: 1.4;
    cursor: text;
}
h1:hover a.anchor,
h2:hover a.anchor,
h3:hover a.anchor,
h4:hover a.anchor,
h5:hover a.anchor,
h6:hover a.anchor {
    text-decoration: none;
}
h1 tt,
h1 code {
    font-size: inherit;
}
h2 tt,
h2 code {
    font-size: inherit;
}
h3 tt,
h3 code {
    font-size: inherit;
}
h4 tt,
h4 code {
    font-size: inherit;
}
h5 tt,
h5 code {
    font-size: inherit;
}
h6 tt,
h6 code {
    font-size: inherit;
}
h1 {
    font-size: 2.25em;
    line-height: 1.2;
    border-bottom: 1px solid #eee;
}
h2 {
    font-size: 1.75em;
    line-height: 1.225;
    border-bottom: 1px solid #eee;
}

/*@media print {
    .typora-export h1,
    .typora-export h2 {
        border-bottom: none;
        padding-bottom: initial;
    }

    .typora-export h1::after,
    .typora-export h2::after {
        content: "";
        display: block;
        height: 100px;
        margin-top: -96px;
        border-top: 1px solid #eee;
    }
}*/

h3 {
    font-size: 1.5em;
    line-height: 1.43;
}
h4 {
    font-size: 1.25em;
}
h5 {
    font-size: 1em;
}
h6 {
   font-size: 1em;
    color: #777;
}
p,
blockquote,
ul,
ol,
dl,
table{
    margin: 0.8em 0;
}
li>ol,
li>ul {
    margin: 0 0;
}
hr {
    height: 2px;
    padding: 0;
    margin: 16px 0;
    background-color: #e7e7e7;
    border: 0 none;
    overflow: hidden;
    box-sizing: content-box;
}

li p.first {
    display: inline-block;
}
ul,
ol {
    padding-left: 30px;
}
ul:first-child,
ol:first-child {
    margin-top: 0;
}
ul:last-child,
ol:last-child {
    margin-bottom: 0;
}
blockquote {
    border-left: 4px solid #dfe2e5;
    padding: 0 15px;
    color: #777777;
}
blockquote blockquote {
    padding-right: 0;
}
table {
    padding: 0;
    word-break: initial;
}
table tr {
    border: 1px solid #dfe2e5;
    margin: 0;
    padding: 0;
}
table tr:nth-child(2n),
thead {
    background-color: #f8f8f8;
}
table th {
    font-weight: bold;
    border: 1px solid #dfe2e5;
    border-bottom: 0;
    margin: 0;
    padding: 6px 13px;
}
table td {
    border: 1px solid #dfe2e5;
    margin: 0;
    padding: 6px 13px;
}
table th:first-child,
table td:first-child {
    margin-top: 0;
}
table th:last-child,
table td:last-child {
    margin-bottom: 0;
}

.CodeMirror-lines {
    padding-left: 4px;
}

.code-tooltip {
    box-shadow: 0 1px 1px 0 rgba(0,28,36,.3);
    border-top: 1px solid #eef2f2;
}

.md-fences,
code,
tt {
    border: 1px solid #e7eaed;
    background-color: #f8f8f8;
    border-radius: 3px;
    padding: 0;
    padding: 2px 4px 0px 4px;
    font-size: 0.9em;
}

code {
    background-color: #f3f4f4;
    padding: 0 2px 0 2px;
}

.md-fences {
    margin-bottom: 15px;
    margin-top: 15px;
    padding-top: 8px;
    padding-bottom: 6px;
}


.md-task-list-item > input {
  margin-left: -1.3em;
}

@media print {
    html {
        font-size: 13px;
    }
    pre {
        page-break-inside: avoid;
        word-wrap: break-word;
    }
}

.md-fences {
	background-color: #f8f8f8;
}
#write pre.md-meta-block {
	padding: 1rem;
    font-size: 85%;
    line-height: 1.45;
    background-color: #f7f7f7;
    border: 0;
    border-radius: 3px;
    color: #777777;
    margin-top: 0 !important;
}

.mathjax-block>.code-tooltip {
	bottom: .375rem;
}

.md-mathjax-midline {
    background: #fafafa;
}

#write>h3.md-focus:before{
	left: -1.5625rem;
	top: .375rem;
}
#write>h4.md-focus:before{
	left: -1.5625rem;
	top: .285714286rem;
}
#write>h5.md-focus:before{
	left: -1.5625rem;
	top: .285714286rem;
}
#write>h6.md-focus:before{
	left: -1.5625rem;
	top: .285714286rem;
}
.md-image>.md-meta {
    /*border: 1px solid #ddd;*/
    border-radius: 3px;
    padding: 2px 0px 0px 4px;
    font-size: 0.9em;
    color: inherit;
}

.md-tag {
    color: #a7a7a7;
    opacity: 1;
}

.md-toc { 
    margin-top:20px;
    padding-bottom:20px;
}

.sidebar-tabs {
    border-bottom: none;
}

#typora-quick-open {
    border: 1px solid #ddd;
    background-color: #f8f8f8;
}

#typora-quick-open-item {
    background-color: #FAFAFA;
    border-color: #FEFEFE #e5e5e5 #e5e5e5 #eee;
    border-style: solid;
    border-width: 1px;
}

/** focus mode */
.on-focus-mode blockquote {
    border-left-color: rgba(85, 85, 85, 0.12);
}

header, .context-menu, .megamenu-content, footer{
    font-family: "Segoe UI", "Arial", sans-serif;
}

.file-node-content:hover .file-node-icon,
.file-node-content:hover .file-node-open-state{
    visibility: visible;
}

.mac-seamless-mode #typora-sidebar {
    background-color: #fafafa;
    background-color: var(--side-bar-bg-color);
}

.mac-os #write{
    caret-color: AccentColor;
}

.md-lang {
    color: #b4654d;
}

/*.html-for-mac {
    --item-hover-bg-color: #E6F0FE;
}*/

#md-notification .btn {
    border: 0;
}

.dropdown-menu .divider {
    border-color: #e5e5e5;
    opacity: 0.4;
}

.ty-preferences .window-content {
    background-color: #fafafa;
}

.ty-preferences .nav-group-item.active {
    color: white;
    background: #999;
}

.menu-item-container a.menu-style-btn {
    background-color: #f5f8fa;
    background-image: linear-gradient( 180deg , hsla(0, 0%, 100%, 0.8), hsla(0, 0%, 100%, 0)); 
}


 @media print { @page {margin: 0 0 0 0;} body.typora-export {padding-left: 0; padding-right: 0;} #write {padding:0;}}
 .typora-export .table-of-contents li, .typora-export .table-of-contents p{
    white-space: normal;
 }
</style>
</head>
<body class='typora-export'>
    <div class="sidebar">
        <div class="logo">
            <img src="../logo.svg" alt="GasFree Logo" />
        </div>
        <nav>
            <ul id="nav-list"></ul>
        </nav>
    </div>
    <div class='typora-export-content'>
        <div id='write' class=''>
        <h1 id='gasfree-developer-documentation' style="text-align: center;display: flex;justify-content: center;align-items: center;">
            <span style="margin-bottom:20px;">GasFree 开发者文档</span>
        </h1>
        <p style="text-align: center;margin-bottom:100px;white-space:unset;">
            <span>Ver: 1.0.2 </span><br />
        </p>
        <div class="table-of-contents">
            <p><span>目   录</span></p>
            <ol style="list-style-type: cjk-ideographic;"><li><p><a href='#overview'><strong><span>项目概述</span></strong></a><span>                                                                                                                                      </span><span>   </span></p></li><li><p><a href='#authorization-process'><strong><span>授权的流程</span></strong></a><span>                                                                                                                                   </span><span>  </span></p></li><li><p><a href='#signature-algorithm-and-provider-endpoint'><strong><span>签名算法及 Provider endpoint</span></strong></a><span>                                </span></p>
                <p style="white-space:normal"><a href='#31-parameters'><span>3.1 相关参数</span></a></p>
                <p style="white-space:normal"><a href='#32-authorization-construction-and-signature'><span>3.2 授权构造及签名</span></a></p>
                <p style="white-space:normal"><a href='#33-endpoint'><span>3.3 Endpoint</span></a></p>
                <p style="white-space:normal"><a href='#34-faucet'><span>3.4 Faucet</span></a></p>
            </li><li><p><a href='#notes'><strong><span>注意事项</span></strong></a><span>                                                                                                                                                          </span><span>  </span></p></li><li><p><a href='#apis'><strong><span>API 接口</span></strong></a><span>                                                                                                                                                           </span></p><p><a href='#api-authentication'><span>API 鉴权</span></a><span>                                                                                                                                    </span></p><p><a href='#api-format-definition'><span>API 格式定义</span></a><span>                                                                                                                               </span></p><p><a href='#get-apiv1configtokenall'><span>GET /api/v1/config/token/all</span></a><span>                                                                                                                     </span><span>	</span></p><p><a href='#get-apiv1configproviderall'><span>GET /api/v1/config/provider/all</span></a><span>                                                                                                                </span><span>	</span></p><p><a href='#get-apiv1addressaccountaddress'><span>GET /api/v1/address/{accountAddress}</span></a><span>                                                                                                  </span></p><p><a href='#post-apiv1gasfreesubmit'><span>POST /api/v1/gasfree/submit</span></a><span>                                                                                                                   </span><span>	</span></p><p><a href='#get-apiv1gasfreetraceid'><span>GET /api/v1/gasfree/{traceId}</span></a><span>                                                                                                                         </span></p></li></ol>
        </div>
      <h1 id='overview'><span>一、项目概述</span></h1><p><span>GasFree 旨在为用户提供一种无需原生币 (如 TRX) 支付交易 Gas 费的 TRC20 / ERC20 转账方案, 方案具体包含 GasFree 账户,  Service-Provider, 钱包以及用户四种角色, 如下图1 所示.</span></p><p><span class='md-image'><img alt='' src='data:image/jpeg;base64,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' referrerPolicy='no-referrer' width="460" height="368"></img></span></p><p style="text-align: center;"><span>图1. GasFree 架构</span></p><p><span>GasFree 账户: 根据特定算法生成的地址, 由用户 EOA 地址控制其权限, 用户可以签名授权该账户的 GasFree 转账.</span></p><p><span>Service-Provider: GasFree 服务提供者, 负责收集用户的 GasFree 转账授权并提交上链, 并替用户支付 Gas 费, Service-Provider 可能会在交易成功后收取一定的手续费.</span></p><p><span>钱包: 钱包在接入 GasFree 服务后, 为终端用户提供 GasFree 账户的资金查询, 转账授权签名等界面功能.</span></p><h2 id='authorization-process'><span>二、授权的流程</span></h2><p><span>钱包集成 GasFree 涉及构造转账授权、签名转账授权及 Provider 代用户提交授权上链等过程. 有关 Provider API 接口的详细信息, 请参阅本文第五节. 集成后, 主要的交互流程如下: </span></p><p><strong><span>1</span><span>.</span><span> 预先准备:</span></strong></p><ul><li><p><span>GasFree Provider 可以支持多种 Token 的 GasFree 授权转账, 调用 </span><a href='#get-apiv1configtokenall'><span>/api/v1/config/token/all</span></a><span> 获取支持的 Token 列表；  </span></p></li><li><p><span>当 GasFree 账户被转入 Provider 暂不支持的 Token 时, 用户可以通过 GasFree 官方提供的提取页面, 将该 Token 提取至自己的 EOA 地址中. 提取页面链接为: </span><a href='https://gasfree.io/withdraw'><span>https://gasfree.io/withdraw</span></a><span>  </span></p></li><li><p><span>GasFree 可以支持多个 Service-Provider, 调用 </span><a href='#get-apiv1configproviderall'><span>/api/v1/config/provider/all</span></a><span> 获取可用的 Service-Provider 列表.  </span></p></li><li><p><span>GasFree 账户默认是未激活状态, 未激活的 GasFree 账户在首次转账时会被自动激活, 激活 GasFree 账户时会额外收取一次“激活手续费”, 后续 GasFree 转账授权仅收取“转账手续费” .</span></p></li></ul><p><strong><span>2</span><span>.</span><span> 准备阶段:</span></strong><span> </span></p><ul><li><p><span>当用户进行 GasFree 转账授权时, 首先调用 </span><a href='#get-apiv1addressaccountaddress'><span>/api/v1/address/{accountAddress}</span></a><span>  查询用户的 GasFree 账户信息, 包括激活状态、余额、nonce, 基于这些信息构建 GasFree 转账授权.</span></p></li></ul><p><strong><span>3</span><span>.</span><span> 签署 GasFree 转账授权:</span></strong><span> </span></p><ul><li><p><span>GasFree 的转账授权的签名算法, 在设计上可支持后续升级签名算法. 当前版本的签名算法兼容 EIP712 规范, 具体授权的签名算法见 3.2 章节.</span></p></li></ul><p><strong><span>4</span><span>.</span><span> 提交 GasFree 转账授权:</span></strong><span> </span></p><ul><li><p><span>使用 </span><a href='#post-apiv1gasfreesubmit'><span>/api/v1/gasfree/submit</span></a><span> 将签名的 GasFree 转账授权发送给 Provider.</span></p></li></ul><p><strong><span>5</span><span>.</span><span> 处理 GasFree 的授权提交返回:</span></strong><span> </span></p><ul><li><p><span>Provider 收到用户提交的 GasFree 转账授权后会进行校验, 并将校验结果立即返回给钱包. 钱包应该处理 Provider 的回复:   </span></p><ul><li><p><span>如果成功, 通知用户授权校验通过, 并附带了一个全网唯一的授权 traceId, 后续可根据该 traceId 跟踪该授权的上链情况.  </span></p></li><li><p><span>如果失败, 则通知用户失败, 该授权已经被丢弃, 本次请求不会触发链上的实际交易.</span></p></li></ul></li></ul><p><strong><span>6</span><span>.</span><span> 转账授权的后续状态监控:</span></strong><span> </span></p><ul><li><p><span>可以调用 </span><a href='#get-apiv1gasfreetraceid'><span>/api/v1/gasfree/{traceId}</span></a><span> 查询 GasFree 转账授权的后续状态变更以及上链情况等.</span></p></li></ul><h2 id='signature-algorithm-and-provider-endpoint'><span>三、签名算法及 Provider endpoint</span></h2><h3 id='31-parameters'><span>3.1 相关参数</span></h3><p><span>GasFree 转账授权涉及到以下参数. 其在 TRON 主网和 Nile 测试网的值见表1.</span></p><figure class='table-figure'><table><thead><tr><th style='text-align:left;' ><span>参数名</span></th><th style='text-align:left;' ><span>TRON </span><span>-</span><span> 主网</span></th><th style='text-align:left;' ><span>TRON </span><span>-</span><span> Nile 测试网</span></th></tr></thead><tbody><tr><td style='text-align:left;' ><span>chainId</span></td><td style='text-align:left;' ><span>​728126428</span></td><td style='text-align:left;' ><span>**********</span></td></tr><tr><td style='text-align:left;' ><span>verifyingContract</span></td><td style='text-align:left;' ><span>TFFAMQLZybALaLb4uxHA9RBE7pxhUAjF3U</span></td><td style='text-align:left;' ><span>THQGuFzL87ZqhxkgqYEryRAd7gqFqL5rdc</span></td></tr></tbody></table></figure><p><span>表1. 转账授权参数</span></p><h3 id='32-authorization-construction-and-signature'><span>3.2 授权构造及签名</span></h3><p><span>公用结构</span></p><ul><li><p><span>MessageDomain</span></p></li></ul><pre class="md-fences md-end-block ty-contain-cm modeLoaded" spellcheck="false" lang="javascript"><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang="javascript"><div style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9px; left: 8px;"><textarea autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0" style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: currentcolor;"></textarea></div><div class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler" cm-not-content="true"></div><div class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer" style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation" style="position: relative; outline: currentcolor;"><div class="CodeMirror-measure"></div><div class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation" style=""><div class="CodeMirror-activeline" style="position: relative;"><div class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-keyword">const</span> <span class="cm-def">Permit712MessageDomain</span> <span class="cm-operator">=</span> {</span></pre></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-property">name</span>: <span class="cm-string">'GasFreeController'</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-property">version</span>: <span class="cm-string">'V1.0.0'</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-property">chainId</span>: <span class="cm-number">**********</span> <span class="cm-comment">// tronWeb.toDecimal('0xcd8690dc'),</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-variable">verifyingContract</span>: <span class="cm-string">'THQGuFzL87ZqhxkgqYEryRAd7gqFqL5rdc'</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">}</span></pre></div></div></div></div></div><div style="position: absolute; height: 0px; width: 1px; border-bottom-width: 0px; border-bottom-style: solid; border-bottom-color: transparent; top: 132px;"></div><div class="CodeMirror-gutters" style="display: none; height: 132px;"></div></div></div></pre><pre class="" spellcheck="false" lang=""><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang=""><div style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9px; left: 8px;"><textarea autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0" style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: currentcolor;"></textarea></div><div class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler" cm-not-content="true"></div><div class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer" style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation" style="position: relative; outline: currentcolor;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">字段说明:</span></pre></div></div></div></div></div></div><div style="position: absolute; height: 0px; width: 1px; border-bottom-width: 0px; border-bottom-style: solid; border-bottom-color: transparent; top: 22px;"></div><div class="CodeMirror-gutters" style="display: none; height: 22px;"></div></div></div></pre><ul><ul><li><p><span>name: 固定值‘GasFreeController’  </span></p></li><li><p><span>version: 固定值 ‘V1.0.0’  </span></p></li><li><p><span>chainId: 十进制格式的 chainId，具体值见3.1章节.  </span></p></li><li><p><span>verifyingContract: GasFreeController 合约地址，具体值见3.1章节.</span></p></li></ul></li><li><p><span>MessageTypes: 固定值</span></p></li></ul><pre class="md-fences md-end-block ty-contain-cm modeLoaded" spellcheck="false" lang="javascript"><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang="javascript"><div style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9px; left: 8px;"><textarea autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0" style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: currentcolor;"></textarea></div><div class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler" cm-not-content="true"></div><div class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer" style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation" style="position: relative; outline: currentcolor;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation" style=""><div class="CodeMirror-activeline" style="position: relative;"><div class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-keyword">const</span> <span class="cm-def">Permit712MessageTypes</span> <span class="cm-operator">=</span> {</span></pre></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-property">PermitTransfer</span>: [</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;  { <span class="cm-property">name</span>: <span class="cm-string">'token'</span>, <span class="cm-property">type</span>: <span class="cm-string">'address'</span> },</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;  { <span class="cm-property">name</span>: <span class="cm-string">'serviceProvider'</span>, <span class="cm-property">type</span>: <span class="cm-string">'address'</span> },</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;  { <span class="cm-property">name</span>: <span class="cm-string">'user'</span>, <span class="cm-property">type</span>: <span class="cm-string">'address'</span> },</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;  { <span class="cm-property">name</span>: <span class="cm-string">'receiver'</span>, <span class="cm-property">type</span>: <span class="cm-string">'address'</span> },</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;  { <span class="cm-property">name</span>: <span class="cm-string">'value'</span>, <span class="cm-property">type</span>: <span class="cm-string">'uint256'</span> },</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;  { <span class="cm-property">name</span>: <span class="cm-string">'maxFee'</span>, <span class="cm-property">type</span>: <span class="cm-string">'uint256'</span> },</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;  { <span class="cm-property">name</span>: <span class="cm-string">'deadline'</span>, <span class="cm-property">type</span>: <span class="cm-string">'uint256'</span> },</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;  { <span class="cm-property">name</span>: <span class="cm-string">'version'</span>, <span class="cm-property">type</span>: <span class="cm-string">'uint256'</span> },</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;  { <span class="cm-property">name</span>: <span class="cm-string">'nonce'</span>, <span class="cm-property">type</span>: <span class="cm-string">'uint256'</span> }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">  ]</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">};</span></pre></div></div></div></div></div><div style="position: absolute; height: 0px; width: 1px; border-bottom-width: 0px; border-bottom-style: solid; border-bottom-color: transparent; top: 286px;"></div><div class="CodeMirror-gutters" style="display: none; height: 286px;"></div></div></div></pre><ul><li><p><span>Message 主体</span></p></li></ul><pre class="md-fences md-end-block ty-contain-cm modeLoaded" spellcheck="false" lang="javascript"><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang="javascript"><div style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9px; left: 8px;"><textarea autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0" style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: currentcolor;"></textarea></div><div class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler" cm-not-content="true"></div><div class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer" style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation" style="position: relative; outline: currentcolor;"><div class="CodeMirror-measure"><span><span>​</span>x</span></div><div class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation" style=""><div class="CodeMirror-activeline" style="position: relative;"><div class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-keyword">const</span> <span class="cm-def">message</span> <span class="cm-operator">=</span> {</span></pre></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-property">token</span>: <span class="cm-string">'TXYZopYRdj2D9XRtbG411XZZ3kM5VkAeBf'</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-property">serviceProvider</span>: <span class="cm-string">'TKtWbdzEq5ss9vTS9kwRhBp5mXmBfBns3E'</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-property">user</span>: <span class="cm-string">'THvMiWQeVPGEMuBtAnuKn2QpuSjqjrGQGu'</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-property">receiver</span>: <span class="cm-string">'TMDKznuDWaZwfZHcM61FVFstyYNmK6Njk1'</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-property">value</span>: <span class="cm-string">'90000000'</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-property">maxFee</span>: <span class="cm-string">'20000000'</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-property">deadline</span>: <span class="cm-string">'1728638679'</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-property">version</span>: <span class="cm-number">1</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-property">nonce</span>: <span class="cm-number">0</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">};</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp="">
</span></span></pre></div></div></div></div></div><div style="position: absolute; height: 0px; width: 1px; border-bottom-width: 0px; border-bottom-style: solid; border-bottom-color: transparent; top: 264px;"></div><div class="CodeMirror-gutters" style="display: none; height: 264px;"></div></div></div></pre><pre class="" spellcheck="false" lang=""><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang=""><div style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9px; left: 8px;"><textarea autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0" style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: currentcolor;"></textarea></div><div class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler" cm-not-content="true"></div><div class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer" style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation" style="position: relative; outline: currentcolor;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">字段说明:</span></pre></div></div></div></div></div></div><div style="position: absolute; height: 0px; width: 1px; border-bottom-width: 0px; border-bottom-style: solid; border-bottom-color: transparent; top: 22px;"></div><div class="CodeMirror-gutters" style="display: none; height: 22px;"></div></div></div></pre><ul><ul><li><p><span>token: 进行转账的 token 地址  </span></p></li><li><p><span>serviceProvider: Service-Provider 地址  </span></p></li><li><p><span>user: 用户 EOA 地址, 非 GasFree 地址  </span></p></li><li><p><span>receiver: 转账的接收地址  </span></p></li><li><p><span>value: 转账金额, 单位为最小单位, 如 90 USDT, 则为 90 </span><span>*</span><span> 10^6  </span></p></li><li><p><span>maxFee: 最大手续费限制 (转账手续费 </span><span>+</span><span> 激活手续费), 单位为最小单位, 如 20 USDT, 则为20 </span><span>*</span><span> 10^6  </span></p></li><li><p><span>deadline: 本次转账授权的失效时间戳, 单位: 秒, 如 Date.now() / 1000  </span></p></li><li><p><span>version: 签名版本, 当前版本为 1  </span></p></li><li><p><span>nonce: 本次转账授权的 nonce 值, 如 0</span></p></li></ul></li></ul><p><span>钱包签名</span><br/><span>利用上述结构，可以调用 Tron 钱包进行 TIP712Message 签名, 即可得到所需的 sig 参数，以 TronLink 为例：</span></p><pre class="md-fences md-end-block ty-contain-cm modeLoaded" spellcheck="false" lang="javascript"><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang="javascript"><div style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9px; left: 8px;"><textarea autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0" style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: currentcolor;"></textarea></div><div class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler" cm-not-content="true"></div><div class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer" style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation" style="position: relative; outline: currentcolor;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation" style=""><div class="CodeMirror-activeline" style="position: relative;"><div class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-keyword">const</span> <span class="cm-def">signature</span> <span class="cm-operator">=</span> <span class="cm-keyword">await</span> <span class="cm-variable">window</span>.<span class="cm-property">tron</span>.<span class="cm-property">tronWeb</span>.<span class="cm-property">trx</span>.<span class="cm-property">_signTypedData</span>(</span></pre></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-variable">Permit712MessageDomain</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-variable">Permit712MessageTypes</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-variable">message</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">); <span class="cm-comment">// 去掉前缀 0x</span></span></pre></div></div></div></div></div><div style="position: absolute; height: 0px; width: 1px; border-bottom-width: 0px; border-bottom-style: solid; border-bottom-color: transparent; top: 110px;"></div><div class="CodeMirror-gutters" style="display: none; height: 110px;"></div></div></div></pre><h3 id='33-endpoint'><span>3.3 Endpoint</span></h3><p><span>钱包/用户可以通过以下 endpoint 与 Provider 进行交互, 包括提交 GasFree 的转账授权, 和查询后续状态等, 也可以将 Provider 暂不支持的token 提取到用户的 EOA 地址.</span></p><figure class='table-figure'><table><thead><tr><th style='text-align:left;' ><span>服务名</span></th><th style='text-align:left;' ><span>TRON </span><span>-</span><span> 主网</span></th><th style='text-align:left;' ><span>TRON </span><span>-</span><span> Nile 测试网</span></th></tr></thead><tbody><tr><td style='text-align:left;' ><span>provider-</span><span>#</span><span>1</span></td><td style='text-align:left;' ><a href='https://open.gasfree.io/tron/'><span>https://open.gasfree.io/tron/</span></a></td><td style='text-align:left;' ><a href='https://open-test.gasfree.io/nile/'><span>https://open-test.gasfree.io/nile/</span></a></td></tr><tr><td style='text-align:left;' ><span>GasFree 官网</span></td><td style='text-align:left;' ><a href='https://gasfree.io'><span>https://gasfree.io</span></a></td><td style='text-align:left;' ><a href='https://test.gasfree.io'><span>https://test.gasfree.io</span></a></td></tr><tr><td style='text-align:left;' ><span>资产提取页面</span></td><td style='text-align:left;' ><a href='https://gasfree.io/withdraw'><span>https://gasfree.io/withdraw</span></a></td><td style='text-align:left;' ><a href='https://test.gasfree.io/withdraw'><span>https://test.gasfree.io/withdraw</span></a></td></tr></tbody></table></figure><p><span>表2. 各个服务的 endpoint</span></p><p><span>注：当前 GasFree 项目仅在 TRON 链提供服务，后续会逐步扩展至以太坊及其他 EVM 兼容链. </span></p><h3 id='34-faucet'><span>3.4 Faucet</span></h3><p><span>Nile 测试网 Faucet： </span><a href='https://nileex.io/join/getJoinPage'><span>https://nileex.io/join/getJoinPage</span></a><span>  开发者可以在此页面领取测试网 TRX 和 USDT。 </span></p><h2 id='notes'><span>四、注意事项</span></h2><p style="color:red;"><strong><span>1</span><span>.</span><span> 建议不要把 TRON 链的 GasFree 测试网环境提供给用户</span></strong></p><p><strong><span>GasFree 在 TRON Nile 测试网上的环境仅支持项目方在集成时作测试联调使用.</span></strong></p><p><strong><span>强烈建议在上线后, 针对普通用户关闭 GasFree 测试环境的入口, 以免用户填错 收款地址, 造成资产损失.</span></strong></p><p><strong><span>2</span><span>.</span><span> 提交转账授权前, 建议钱包先进行余额、状态校验，详见 </span><a href='#get-apiv1addressaccountaddress'><span>GasFree账户接口</span></a><span> 定义.</span></strong><span> </span></p><p><strong><span>文中涉及资产余额、金额处, 如无特殊说明，均采用最小单位.</span></strong></p><h2 id='apis'><span>五、API 接口</span></h2><h3 id='api-authentication'><span>API 鉴权</span></h3><p><span>GasFree 项目接入方申请接入后将会得到一对秘钥 API Key 和 API Secret. 该秘钥对 API 请求进行签名，Api Secret请务必妥善保管，GasFree 服务端对请求进行验证，签名信息通过 http header 传递.</span></p><p><span>API Key 和 API Secret 可以通过 </span><a href='https://docs.google.com/forms/d/e/1FAIpQLSc5EB1X8JN7LA4SAVAG99VziXEY6Kv6JxmlBry9rUBlwI-GaQ/viewform?usp=dialog'><span>这里</span></a><span> 提交申请.</span></p><p><strong><span>http header 格式定义如下:</span></strong></p><pre class="md-fences md-end-block ty-contain-cm modeLoaded" spellcheck="false" lang="javascript"><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang="javascript"><div style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9px; left: 8px;"><textarea autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0" style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: currentcolor;"></textarea></div><div class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler" cm-not-content="true"></div><div class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer" style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation" style="position: relative; outline: currentcolor;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">{</span></pre></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-string">"Timestamp"</span>: <span class="cm-number">1731912286</span>, <span class="cm-comment">// 单位: 秒</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-string">"Authorization"</span>: <span class="cm-string">"ApiKey {api_key}:{signature}"</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">}</span></pre></div></div></div></div></div><div style="position: absolute; height: 0px; width: 1px; border-bottom-width: 0px; border-bottom-style: solid; border-bottom-color: transparent; top: 88px;"></div><div class="CodeMirror-gutters" style="display: none; height: 88px;"></div></div></div></pre><p><strong><span>API 签名及鉴权算法</span></strong></p><ol start='' ><li><p><span>构建待签名字符串：包括 请求方法, 请求路径, 时间戳  </span></p></li><li><p><span>计算签名：使用 HMAC-SHA256 算法和 API Secret 对待签名字符串进行哈希运算，对结果进行 base64 编码  </span></p></li><li><p><span>将生成的签名附加到请求头.</span></p></li></ol><pre class="md-fences md-end-block ty-contain-cm modeLoaded" spellcheck="false" lang="py" style="break-inside: unset;"><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang="py"><div style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9px; left: 8px;"><textarea autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0" style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: currentcolor;"></textarea></div><div class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler" cm-not-content="true"></div><div class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer" style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation" style="position: relative; outline: currentcolor;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation" style=""><div class="CodeMirror-activeline" style="position: relative;"><div class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-keyword">import</span> <span class="cm-variable">hmac</span></span></pre></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-keyword">import</span> <span class="cm-variable">hashlib</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-keyword">import</span> <span class="cm-variable">base64</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-keyword">import</span> <span class="cm-variable">time</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-keyword">import</span> <span class="cm-variable">requests</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp="">
</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-variable">API_KEY</span> <span class="cm-operator">=</span> <span class="cm-string">'YOUR_API_KEY'</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-variable">API_SECRET</span> <span class="cm-operator">=</span> <span class="cm-string">'YOUR_API_SECRET'</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp="">
</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-variable">method</span> <span class="cm-operator">=</span> <span class="cm-string">'GET'</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-variable">path</span> <span class="cm-operator">=</span> <span class="cm-string">'/api/v1/config/token/all'</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-variable">timestamp</span> <span class="cm-operator">=</span> <span class="cm-builtin">int</span>(<span class="cm-variable">time</span>.<span class="cm-property">time</span>())</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-variable">message</span> <span class="cm-operator">=</span> <span class="cm-string">f'</span>{<span class="cm-variable">method</span>}{<span class="cm-variable">path</span>}{<span class="cm-variable">timestamp</span>}<span class="cm-string">'</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-variable">signature</span> <span class="cm-operator">=</span> <span class="cm-variable">base64</span>.<span class="cm-property">b64encode</span>(</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">hmac</span>.<span class="cm-property">new</span>(</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">API_SECRET</span>.<span class="cm-property">encode</span>(<span class="cm-string">'utf-8'</span>), </span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">message</span>.<span class="cm-property">encode</span>(<span class="cm-string">'utf-8'</span>), </span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">hashlib</span>.<span class="cm-property">sha256</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp;  ).<span class="cm-property">digest</span>()</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;  ).<span class="cm-property">decode</span>(<span class="cm-string">'utf-8'</span>)</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp="">
</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-variable">url</span> <span class="cm-operator">=</span> <span class="cm-string">'https://test-nile.gasfree.io'</span> <span class="cm-operator">+</span> <span class="cm-variable">path</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-variable">headers</span> <span class="cm-operator">=</span> {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-string">'Timestamp'</span>: <span class="cm-string">f'</span>{<span class="cm-variable">timestamp</span>}<span class="cm-string">'</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-string">'Authorization'</span>: <span class="cm-string">f'ApiKey </span>{<span class="cm-variable">API_KEY</span>}<span class="cm-string">:</span>{<span class="cm-variable">signature</span>}<span class="cm-string">'</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">}</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp="">
</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-variable">response</span> <span class="cm-operator">=</span> <span class="cm-variable">requests</span>.<span class="cm-property">get</span>(<span class="cm-variable">url</span>, <span class="cm-variable">headers</span><span class="cm-operator">=</span><span class="cm-variable">headers</span>)</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-builtin">print</span>(<span class="cm-variable">response</span>.<span class="cm-property">json</span>())</span></pre></div></div></div></div></div><div style="position: absolute; height: 0px; width: 1px; border-bottom-width: 0px; border-bottom-style: solid; border-bottom-color: transparent; top: 638px;"></div><div class="CodeMirror-gutters" style="display: none; height: 638px;"></div></div></div></pre><h3 id='api-format-definition'><span>API 格式定义</span></h3><p><span>在服务正常运行情况下, API 接口返回的 http code 均为 200, 参数错误或者运行时错误均体现在 http body 内.</span></p><p><strong><span>http body格式定义如下:</span></strong></p><pre class="md-fences md-end-block ty-contain-cm modeLoaded" spellcheck="false" lang="javascript"><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang="javascript"><div style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9px; left: 8px;"><textarea autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0" style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: currentcolor;"></textarea></div><div class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler" cm-not-content="true"></div><div class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer" style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation" style="position: relative; outline: currentcolor;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation" style=""><div class="CodeMirror-activeline" style="position: relative;"><div class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">{</span></pre></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-string">"code"</span>: <span class="cm-number">200</span>, <span class="cm-comment">// 请求是否正常返回, 200表示正常, 400表示输入错误导致失败, 500表示运行时错误导致失败</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-string">"reason"</span>: <span class="cm-atom">null</span>, <span class="cm-comment">// 异常名称.</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-string">"message"</span>: <span class="cm-string">""</span>, <span class="cm-comment">// 异常信息, 通常为组合的字符串</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-string">"data"</span>: <span class="cm-string">""</span> <span class="cm-comment">// 正常返回则为API接口的返回数据, 否则为null</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">}</span></pre></div></div></div></div></div><div style="position: absolute; height: 0px; width: 1px; border-bottom-width: 0px; border-bottom-style: solid; border-bottom-color: transparent; top: 132px;"></div><div class="CodeMirror-gutters" style="display: none; height: 132px;"></div></div></div></pre><p><strong><span>错误返回示例:</span></strong></p><pre class="md-fences md-end-block ty-contain-cm modeLoaded" spellcheck="false" lang="javascript"><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang="javascript"><div style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9px; left: 8px;"><textarea autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0" style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: currentcolor;"></textarea></div><div class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler" cm-not-content="true"></div><div class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer" style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation" style="position: relative; outline: currentcolor;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation" style=""><div class="CodeMirror-activeline" style="position: relative;"><div class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">{</span></pre></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-string">"code"</span>: <span class="cm-number">400</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-string">"reason"</span>: <span class="cm-string">"GasFreeAddressNotFoundException"</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-string">"message"</span>: <span class="cm-string">"123"</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-string">"data"</span>: <span class="cm-atom">null</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">}</span></pre></div></div></div></div></div><div style="position: absolute; height: 0px; width: 1px; border-bottom-width: 0px; border-bottom-style: solid; border-bottom-color: transparent; top: 132px;"></div><div class="CodeMirror-gutters" style="display: none; height: 132px;"></div></div></div></pre><p><strong><span>成功返回示例:</span></strong></p><pre class="md-fences md-end-block ty-contain-cm modeLoaded" spellcheck="false" lang="javascript" style="break-inside: unset;"><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang="javascript"><div style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9px; left: 8px;"><textarea autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0" style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: currentcolor;"></textarea></div><div class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler" cm-not-content="true"></div><div class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer" style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation" style="position: relative; outline: currentcolor;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation" style=""><div class="CodeMirror-activeline" style="position: relative;"><div class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp="">
</span></span></pre></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">{</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-string">"code"</span>: <span class="cm-number">200</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-string">"reason"</span>: <span class="cm-atom">null</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-string">"message"</span>: <span class="cm-atom">null</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-string">"data"</span>: {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-string">"tokens"</span>: [</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;  {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-string cm-property">"tokenAddress"</span>: <span class="cm-string">"TXYZopYRdj2D9XRtbG411XZZ3kM5VkAeBf"</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-string cm-property">"createdAt"</span>: <span class="cm-string">"2024-09-10T09:46:24.801+00:00"</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-string cm-property">"updatedAt"</span>: <span class="cm-string">"2024-09-11T06:57:10.244+00:00"</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-string cm-property">"activateFee"</span>: <span class="cm-number">10000000</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-string cm-property">"transferFee"</span>: <span class="cm-number">10000000</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-string cm-property">"supported"</span>: <span class="cm-atom">true</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-string cm-property">"symbol"</span>: <span class="cm-string">"USDT"</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-string cm-property">"decimal"</span>: <span class="cm-number">6</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;  ]</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">}</span></pre></div></div></div></div></div><div style="position: absolute; height: 0px; width: 1px; border-bottom-width: 0px; border-bottom-style: solid; border-bottom-color: transparent; top: 440px;"></div><div class="CodeMirror-gutters" style="display: none; height: 440px;"></div></div></div></pre><h3 id='get-apiv1configtokenall'><span>GET /api/v1/config/token/all</span></h3><p><span>provider </span><span>#</span><span>1 在TRON 主网服务的完整请求url 为:  </span><a href='https://open.gasfree.io/tron/api/v1/config/token/all' target='_blank' class='url'>https://open.gasfree.io/tron/api/v1/config/token/all</a></p><p><span>获取所有支持的 token 合约列表</span></p><ul><li><p><strong><span>请求参数:</span></strong><span> 无  </span></p></li><li><p><strong><span>返回结果:</span></strong><span> tokens 包含所有支持的 token 列表</span><br/><span>token 结构体字段说明:  </span></p><ul><li><p><span>tokenAddress: token 合约地址  </span></p></li><li><p><span>activateFee: 以转账币种支付的激活手续费，单位为该 Token 最小单位. 该数值后续可以根据实际情况调整.  </span></p></li><li><p><span>transferFee: 以转账币种支付的转账手续费，单位为该 Token 最小单位. 该数值后续可以根据实际情况调整.  </span></p></li><li><p><span>symbol: token 名称  </span></p></li><li><p><span>decimal: token 精度</span></p></li></ul></li></ul><p><span>返回示例:</span></p><pre class="md-fences md-end-block ty-contain-cm modeLoaded" spellcheck="false" lang="javascript" style="break-inside: unset;"><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang="javascript"><div style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9px; left: 8px;"><textarea autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0" style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: currentcolor;"></textarea></div><div class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler" cm-not-content="true"></div><div class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer" style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation" style="position: relative; outline: currentcolor;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation" style=""><div class="CodeMirror-activeline" style="position: relative;"><div class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">{</span></pre></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-string">"code"</span>: <span class="cm-number">200</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-string">"reason"</span>: <span class="cm-atom">null</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-string">"message"</span>: <span class="cm-atom">null</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-string">"data"</span>: {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-string">"tokens"</span>: [</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;  {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-string cm-property">"tokenAddress"</span>: <span class="cm-string">"TXYZopYRdj2D9XRtbG411XZZ3kM5VkAeBf"</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-string cm-property">"createdAt"</span>: <span class="cm-string">"2024-10-09T08:14:12.560+00:00"</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-string cm-property">"updatedAt"</span>: <span class="cm-string">"2024-10-09T08:14:12.560+00:00"</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-string cm-property">"activateFee"</span>: <span class="cm-number">10000000</span>, <span class="cm-comment">// 代表 10 USDT</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-string cm-property">"transferFee"</span>: <span class="cm-number">10000000</span>, <span class="cm-comment">// 代表 10 USDT</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-string cm-property">"supported"</span>: <span class="cm-atom">true</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-string cm-property">"symbol"</span>: <span class="cm-string">"USDT"</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-string cm-property">"decimal"</span>: <span class="cm-number">6</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;  ]</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">}</span></pre></div></div></div></div></div><div style="position: absolute; height: 0px; width: 1px; border-bottom-width: 0px; border-bottom-style: solid; border-bottom-color: transparent; top: 418px;"></div><div class="CodeMirror-gutters" style="display: none; height: 418px;"></div></div></div></pre><h3 id='get-apiv1configproviderall'><span>GET /api/v1/config/provider/all</span></h3><p><span>获取所有支持的服务商列表</span></p><ul><li><p><strong><span>请求参数:</span></strong><span> 无  </span></p></li><li><p><strong><span>返回结果:</span></strong><span> providers 包含所有支持的 Service-Provider 的列表</span><br/><span>provider 结构体字段说明:  </span></p><ul><li><p><span>address: Service-Provider 的地址  </span></p></li><li><p><span>name: Provider 的名称  </span></p></li><li><p><span>icon: Provider 的图标  </span></p></li><li><p><span>website: Provider 的网站  </span></p></li><li><p><span>config: 该 provider 的系统参数  </span></p><ul><li><p><span>maxPendingTransfer: 允许用户提交的最多等待上链中的转账授权数, 超过该数量提交报错. 必须等到之前的转账授权上链并执行成功后才可以继续发送.（当前同一账号只支持一笔状态 pending 的转账授权）  </span></p></li><li><p><span>minDeadlineDuration: 最小 deadline 间隔, deadline 距当前时间小于该值的转账授权会被拒绝, 单位秒  </span></p></li><li><p><span>maxDeadlineDuration: 最大 deadline 间隔, deadline 距当前时间大于该值的转账授权会被拒绝, 单位秒  </span></p></li><li><p><span>defaultDeadlineDuration: 默认 deadline 间隔, 推荐值</span></p></li></ul><p>&nbsp;</p></li></ul></li></ul><p><span>返回示例:</span></p><pre class="md-fences md-end-block ty-contain-cm modeLoaded" spellcheck="false" lang="javascript" style="break-inside: unset;"><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang="javascript"><div style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9px; left: 8px;"><textarea autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0" style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: currentcolor;"></textarea></div><div class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler" cm-not-content="true"></div><div class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer" style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation" style="position: relative; outline: currentcolor;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation" style=""><div class="CodeMirror-activeline" style="position: relative;"><div class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">{</span></pre></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-string">"code"</span>: <span class="cm-number">200</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-string">"reason"</span>: <span class="cm-atom">null</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-string">"message"</span>: <span class="cm-atom">null</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-string">"data"</span>: {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-string">"providers"</span>: [</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;  {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-string cm-property">"address"</span>: <span class="cm-string">"TQ6qStrS2ZJ96gieZJC8AurTxwqJETmjfp"</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-string cm-property">"name"</span>: <span class="cm-string">"Provider-1"</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-string cm-property">"icon"</span>: <span class="cm-string">""</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-string cm-property">"website"</span>: <span class="cm-string">""</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-string cm-property">"config"</span>: {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-string cm-property">"maxPendingTransfer"</span>: <span class="cm-number">1</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-string cm-property">"minDeadlineDuration"</span>: <span class="cm-number">60</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-string cm-property">"maxDeadlineDuration"</span>: <span class="cm-number">600</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-string cm-property">"defaultDeadlineDuration"</span>: <span class="cm-number">180</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;  ]</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">}</span></pre></div></div></div></div></div><div style="position: absolute; height: 0px; width: 1px; border-bottom-width: 0px; border-bottom-style: solid; border-bottom-color: transparent; top: 462px;"></div><div class="CodeMirror-gutters" style="display: none; height: 462px;"></div></div></div></pre><h3 id='get-apiv1addressaccountaddress'><span>GET /api/v1/address/{accountAddress}</span></h3><p><span>查询用户 GasFree 账户的相关信息, 包括是否已激活, nonce 值, 支持的 token 及资产. </span></p><ol start='' ><li><p><strong><span>应当从链上查询 GasFree 地址最新资产,  结合该接口给出的 frozen 值, 限制用户最大转出金额, 否则可能会造成提交失败或转账失败的情况.</span></strong><span>  </span></p></li></ol><ul><li><p><strong><span>请求参数:</span></strong><span> accountAddress: 用户 EOA 地址  </span></p></li><li><p><strong><span>返回结果:</span></strong><span> 用户的 GasFree 账户的资产信息</span><br/><span>字段说明:  </span></p><ul><li><p><span>accountAddress: 用户地址  </span></p></li><li><p><span>gasFreeAddress: 用户的 GasFree 账户地址  </span></p></li><li><p><span>active: 该 GasFree 账户是否已经激活  </span></p></li><li><p><span>nonce: 下一笔转账推荐填写的 nonce 值, 由于可能存在待上链的转账授权，所以链上 nonce 不一定适用, 后端综合链上和队列情况提供推荐 nonce 值.  </span></p></li><li><p><strong><span>allowSubmit: 当前是否允许用户继续提交转账授权.</span></strong><span>  </span></p></li><li><p><span>assets: 用户正在通过gasfree服务商处理的资产情况  </span></p><ul><li><p><span>tokenAddress: token 合约地址  </span></p></li><li><p><span>tokenSymbol: token 名称  </span></p></li><li><p><span>activateFee: 该 token 激活手续费，单位为该 Token 最小单位  </span></p></li><li><p><span>transferFee: 该 token 转账手续费，单位为该 Token 最小单位  </span></p></li><li><p><span>decimal: 该 token 的精度  </span></p></li><li><p><span>frozen: 当前正在进行中的 GasFree 转账金额, 包括手续费</span></p></li></ul></li></ul></li></ul><p><span>返回示例:</span></p><pre class="md-fences md-end-block ty-contain-cm modeLoaded" spellcheck="false" lang="javascript" style="break-inside: unset;"><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang="javascript"><div style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9px; left: 8px;"><textarea autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0" style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: currentcolor;"></textarea></div><div class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler" cm-not-content="true"></div><div class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer" style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation" style="position: relative; outline: currentcolor;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation" style=""><div class="CodeMirror-activeline" style="position: relative;"><div class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">{</span></pre></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-string">"code"</span>: <span class="cm-number">200</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-string">"reason"</span>: <span class="cm-atom">null</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-string">"message"</span>: <span class="cm-atom">null</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-string">"data"</span>: {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-string">"accountAddress"</span>: <span class="cm-string">"TKtWbdzEq5ss9vTS9kwRhBp5mXmBfBns3E"</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-string">"gasFreeAddress"</span>: <span class="cm-string">"TLGVf7MRsLG7XxBkJKy8wnCVcDnAeXYNCb"</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-string">"active"</span>: <span class="cm-atom">true</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-string">"nonce"</span>: <span class="cm-number">1</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-string">"allow_submit"</span>: <span class="cm-atom">false</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-string">"assets"</span>: [</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;  {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-string cm-property">"tokenAddress"</span>: <span class="cm-string">"TXYZopYRdj2D9XRtbG411XZZ3kM5VkAeBf"</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-string cm-property">"tokenSymbol"</span>: <span class="cm-string">"USDT"</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-string cm-property">"activateFee"</span>: <span class="cm-number">10000000</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-string cm-property">"transferFee"</span>: <span class="cm-number">10000000</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-string cm-property">"decimal"</span>: <span class="cm-number">6</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-string cm-property">"frozen"</span>: <span class="cm-number">0</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;  ]</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">}</span></pre></div></div></div></div></div><div style="position: absolute; height: 0px; width: 1px; border-bottom-width: 0px; border-bottom-style: solid; border-bottom-color: transparent; top: 484px;"></div><div class="CodeMirror-gutters" style="display: none; height: 484px;"></div></div></div></pre><h3 id='post-apiv1gasfreesubmit'><span>POST /api/v1/gasfree/submit</span></h3><p><span>发起 GasFree 转账</span></p><ul><li><p><strong><span>请求参数:</span></strong><span>   </span></p><ul><li><p><span>token: 进行转账的 token 地址  </span></p></li><li><p><span>serviceProvider: Service-Provider 地址  </span></p></li><li><p><span>user: 用户 EOA 地址, 非 GasFree 地址  </span></p></li><li><p><span>receiver: 转账的接收地址  </span></p></li><li><p><span>value: 转账金额  </span></p></li><li><p><span>maxFee: 最大手续费限制 (转账手续费 </span><span>+</span><span> 激活手续费)  </span></p></li><li><p><span>deadline: 本次转账的失效时间戳, 单位: 秒  </span></p></li><li><p><span>version: 转账授权签名版本  </span></p></li><li><p><span>nonce: 本次转账授权的 nonce 值  </span></p></li><li><p><span>sig: 用户对 GasFree 转账授权的签名</span></p></li></ul></li></ul><p><span>请求示例:</span></p><pre class="md-fences md-end-block ty-contain-cm modeLoaded" spellcheck="false" lang="javascript"><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang="javascript"><div style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9px; left: 8px;"><textarea autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0" style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: currentcolor;"></textarea></div><div class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler" cm-not-content="true"></div><div class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer" style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation" style="position: relative; outline: currentcolor;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation" style=""><div class="CodeMirror-activeline" style="position: relative;"><div class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">{</span></pre></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-string">"token"</span>: <span class="cm-string">"TXYZopYRdj2D9XRtbG411XZZ3kM5VkAeBf"</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-string">"serviceProvider"</span>: <span class="cm-string">"TCETRh3aED4kdkaYQY7CcxeTJtrQvwBpNT"</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-string">"user"</span>: <span class="cm-string">"TKtWbdzEq5ss9vTS9kwRhBp5mXmBfBns3E"</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-string">"receiver"</span>: <span class="cm-string">"TEkj3ndMVEmFLYaFrATMwMjBRZ1EAZkucT"</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-string">"value"</span>: <span class="cm-number">100000</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-string">"maxFee"</span>: <span class="cm-number">1000000</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-string">"deadline"</span>: <span class="cm-number">1728638679</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-string">"version"</span>: <span class="cm-number">1</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-string">"nonce"</span>: <span class="cm-number">9</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-string">"sig"</span>: <span class="cm-string">"9dfd3638e03af56bc93fa619e20e1e743f6b5c0d9a49bd340a94e27f6d6a6413618cc8481b9d5816a793982d8047daa0badbade02f92814e78a9894efd9877341b"</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">}</span></pre></div></div></div></div></div><div style="position: absolute; height: 0px; width: 1px; border-bottom-width: 0px; border-bottom-style: solid; border-bottom-color: transparent; top: 308px;"></div><div class="CodeMirror-gutters" style="display: none; height: 308px;"></div></div></div></pre><ul><li><p><strong><span>返回结果:</span></strong><span> 本次 GasFree 转账授权记录的基本信息</span><br/><span>字段说明:  </span></p><ul><li><p><span>id: GasFree 转账授权记录的 traceId, 不是 transactionId  </span></p></li><li><p><span>accountAddress: 用户 EOA 地址  </span></p></li><li><p><span>gasFreeAddress: 用户的 GasFree 账户地址  </span></p></li><li><p><span>providerAddress: 服务商地址  </span></p></li><li><p><span>targetAddress: 接收方地址  </span></p></li><li><p><span>tokenAddress: 转账 token 合约地址  </span></p></li><li><p><span>amount: 转账数量  </span></p></li><li><p><span>maxFee: 最大手续费限制  </span></p></li><li><p><span>signature: 用户签名  </span></p></li><li><p><span>version: 转账授权签名版本   </span></p></li><li><p><span>nonce: 转账授权指定的 nonce 值  </span></p></li><li><p><span>expiredAt: 本次转账的失效时间  </span></p></li><li><p><span>state: 本次转账的当前状态, 有效值如下:  </span></p><ul><li><p><span>WAITING, // 未开始  </span></p></li><li><p><span>INPROGRESS, // 进行中  </span></p></li><li><p><span>CONFIRMING, // 确认中  </span></p></li><li><p><span>SUCCEED, // 成功  </span></p></li><li><p><span>FAILED, // 失败  </span></p></li></ul></li><li><p><span>estimatedActivateFee: 预计地址激活手续费  </span></p></li><li><p><span>estimatedTransferFee: 预计转账手续费</span></p></li></ul></li></ul><p><span>返回示例:</span></p><pre class="md-fences md-end-block ty-contain-cm modeLoaded" spellcheck="false" lang="javascript" style="break-inside: unset;"><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang="javascript"><div style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9px; left: 8px;"><textarea autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0" style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: currentcolor;"></textarea></div><div class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler" cm-not-content="true"></div><div class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer" style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation" style="position: relative; outline: currentcolor;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation" style=""><div class="CodeMirror-activeline" style="position: relative;"><div class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">{</span></pre></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-string">"code"</span>: <span class="cm-number">200</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-string">"reasone"</span>: <span class="cm-atom">null</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-string">"message"</span>: <span class="cm-atom">null</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-string">"data"</span>: {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-tab" role="presentation" cm-text="	">  </span><span class="cm-string">"id"</span>: <span class="cm-string">"6ab4c27c-f66b-4328-b40f-ffdc6cf1ca60"</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-tab" role="presentation" cm-text="	">  </span><span class="cm-string">"createdAt"</span>: <span class="cm-string">"2024-09-10T08:11:50.822+00:00"</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-tab" role="presentation" cm-text="	">  </span><span class="cm-string">"updatedAt"</span>: <span class="cm-string">"2024-09-10T08:11:50.822+00:00"</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-tab" role="presentation" cm-text="	">  </span><span class="cm-string">"accountAddress"</span>: <span class="cm-string">"TKtWbdzEq5ss9vTS9kwRhBp5mXmBfBns3E"</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-tab" role="presentation" cm-text="	">  </span><span class="cm-string">"gasFreeAddress"</span>: <span class="cm-string">"TLGVf7MRsLG7XxBkJKy8wnCVcDnAeXYNCb"</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-tab" role="presentation" cm-text="	">  </span><span class="cm-string">"providerAddress"</span>: <span class="cm-string">"TQ6qStrS2ZJ96gieZJC8AurTxwqJETmjfp"</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-tab" role="presentation" cm-text="	">  </span><span class="cm-string">"targetAddress"</span>: <span class="cm-string">"TQ6qStrS2ZJ96gieZJC8AurTxwqJETmjfp"</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-tab" role="presentation" cm-text="	">  </span><span class="cm-string">"tokenAddress"</span>: <span class="cm-string">"TXYZopYRdj2D9XRtbG411XZZ3kM5VkAeBf"</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-tab" role="presentation" cm-text="	">  </span><span class="cm-string">"amount"</span>: <span class="cm-number">1000000</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-tab" role="presentation" cm-text="	">  </span><span class="cm-string">"maxFee"</span>: <span class="cm-number">1000000</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-tab" role="presentation" cm-text="	">  </span><span class="cm-string">"signature"</span>: <span class="cm-string">"f5bb43b90a5295759819edb029a1c2d6cd50acc9bd4283fd73c4be16fe163d154ec4f06fd70c8d63cc2ff2346ea1c86abf019da944fb62eb28fe0a8f6e12e1931c"</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-tab" role="presentation" cm-text="	">  </span><span class="cm-string">"nonce"</span>: <span class="cm-number">0</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-tab" role="presentation" cm-text="	">  </span><span class="cm-string">"expiredAt"</span>: <span class="cm-string">"2024-09-11T08:05:08.000+00:00"</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-tab" role="presentation" cm-text="	">  </span><span class="cm-string">"state"</span>: <span class="cm-string">"WAITING"</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-string">"estimatedActivateFee"</span>: <span class="cm-number">1000</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-string">"estimateTransferFee"</span>: <span class="cm-number">300</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">}</span></pre></div></div></div></div></div><div style="position: absolute; height: 0px; width: 1px; border-bottom-width: 0px; border-bottom-style: solid; border-bottom-color: transparent; top: 550px;"></div><div class="CodeMirror-gutters" style="display: none; height: 550px;"></div></div></div></pre><p><span>错误示例:</span></p><pre class="md-fences md-end-block ty-contain-cm modeLoaded" spellcheck="false" lang="javascript"><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang="javascript"><div style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9px; left: 8px;"><textarea autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0" style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: currentcolor;"></textarea></div><div class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler" cm-not-content="true"></div><div class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer" style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation" style="position: relative; outline: currentcolor;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation" style=""><div class="CodeMirror-activeline" style="position: relative;"><div class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">{</span></pre></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-string">"code"</span>: <span class="cm-number">400</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-string">"reason"</span>: <span class="cm-string">"DeadlineExceededException"</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-string">"message"</span>: <span class="cm-string">"deadline exceeded"</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-string">"data"</span>: <span class="cm-atom">null</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">}</span></pre></div></div></div></div></div><div style="position: absolute; height: 0px; width: 1px; border-bottom-width: 0px; border-bottom-style: solid; border-bottom-color: transparent; top: 132px;"></div><div class="CodeMirror-gutters" style="display: none; height: 132px;"></div></div></div></pre><ul><li><p><strong><span>错误类型:</span></strong><span>   </span></p><ul><li><p><span>ProviderAddressNotMatchException 服务商地址不匹配  </span></p></li><li><p><span>DeadlineExceededException 授权已经过期  </span></p></li><li><p><span>InvalidSignatureException 签名错误  </span></p></li><li><p><span>UnsupportedTokenException 转账 token 不支持  </span></p></li><li><p><span>TooManyPendingTransferException 正在等待上链中的转账过多  </span></p></li><li><p><span>VersionNotSupportedException gasfree 转账授权签名版本不支持  </span></p></li><li><p><span>NonceNotMatchException 转账 nonce 不匹配  </span></p></li><li><p><span>MaxFeeExceededException 预计手续费超过最大手续费限制  </span></p></li><li><p><span>InsufficientBalanceException 余额不足</span></p></li></ul></li></ul><h3 id='get-apiv1gasfreetraceid'><span>GET /api/v1/gasfree/{traceId}</span></h3><p><span>查询某一笔指定的 GasFree 转账授权的详情, </span><strong><span>如果在向链上提交之前, 或者提交前校验失败, 则 txn 相关字段均为空.</span></strong></p><ul><li><p><strong><span>必填参数:</span></strong><span>  </span></p><ul><li><p><span>traceId: 查询 GasFree 转账授权的 traceId, 路径参数  </span></p></li></ul></li><li><p><strong><span>返回结果:</span></strong><span> 该 GasFree 转账授权的详细情况</span><br/><span>字段说明:   </span></p><ul><li><p><span>id: GasFree 转账授权记录的 traceId, 非 transactionId  </span></p></li><li><p><span>createdAt: 该转账授权创建时间  </span></p></li><li><p><span>accountAddress: 用户 EOA 地址  </span></p></li><li><p><span>gasFreeAddress: 用户的 GasFree 账户地址  </span></p></li><li><p><span>providerAddress: 服务商地址  </span></p></li><li><p><span>targetAddress: 接收方地址  </span></p></li><li><p><span>nonce: 本次 GasFree 转账授权的 nonce 值  </span></p></li><li><p><span>tokenAddress: 转账 token 合约地址  </span></p></li><li><p><span>amount: 用户转账到账金额  </span></p></li><li><p><span>expiredAt: 本次转账的失效时间  </span></p></li><li><p><span>state: 本次转账的当前状态, 有效值如下:  </span></p><ul><li><p><span>WAITING, // 未开始  </span></p></li><li><p><span>INPROGRESS, // 进行中  </span></p></li><li><p><span>CONFIRMING, // 确认中  </span></p></li><li><p><span>SUCCEED, // 成功  </span></p></li><li><p><span>FAILED, // 失败  </span></p></li></ul></li><li><p><span>estimatedActivateFee: 预计激活手续费  </span></p></li><li><p><span>estimatedTransferFee: 预计转账手续费  </span></p></li><li><p><span>estimatedTotalFee: 预计的总手续费  </span></p></li><li><p><span>estimatedTotalCost: 预计的用户总共需要支付的金额, 手续费 </span><span>+</span><span> 转账金额  </span></p></li><li><p><span>txnHash: 对应的链上交易的 transactionId  </span></p></li><li><p><span>txnBlockNum: 对应的链上交易所在区块高度  </span></p></li><li><p><span>txnBlockTimestamp: 对应的链上交易所在区块时间戳, 单位为毫秒  </span></p></li><li><p><span>txnState: 对应链上交易的状态, 有效值如下:  </span></p><ul><li><p><span>INIT，// 初始状态  </span></p></li><li><p><span>NOT</span><span>_</span><span>ON</span><span>_</span><span>CHAIN, // 未上链  </span></p></li><li><p><span>ON</span><span>_</span><span>CHAIN, // 已上链, 未固化  </span></p></li><li><p><span>SOLIDITY, // 已固化  </span></p></li><li><p><span>ON</span><span>_</span><span>CHAIN</span><span>_</span><span>FAILED, // 上链失败  </span></p></li></ul></li><li><p><span>txnActivateFee: 实际消耗的激活手续费  </span></p></li><li><p><span>txnTransferFee: 实际消耗的转账手续费  </span></p></li><li><p><span>txnTotalFee: 实际消耗的总手续费  </span></p></li><li><p><span>txnAmount: 实际转账到账金额  </span></p></li><li><p><span>txnTotalCost: 实际用户总共支付的金额, 手续费 </span><span>+</span><span> 转账金额</span></p></li></ul></li></ul></div></div>
    <script>
        // 生成导航目录
        document.addEventListener('DOMContentLoaded', function() {
            const headings = document.querySelectorAll('#write h1, #write h2, #write h3');
            const navList = document.getElementById('nav-list');
            
            headings.forEach((heading, index) => {
                const link = document.createElement('a');
                link.textContent = heading.textContent;
                link.href = `#${heading.id}`;
                
                const li = document.createElement('li');
                li.appendChild(link);
                
                // 根据标题级别添加缩进
                if (heading.tagName === 'H2') {
                    li.style.paddingLeft = '1rem';
                } else if (heading.tagName === 'H3') {
                    li.style.paddingLeft = '2rem';
                }
                
                navList.appendChild(li);
                
                // 添加点击滚动效果
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    heading.scrollIntoView({ behavior: 'smooth' });
                });
            });
        });
    </script>
</body>
</html>