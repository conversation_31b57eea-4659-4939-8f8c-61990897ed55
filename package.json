{"name": "gas-free-web", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "cross-env VITE_APP_ENV=test vite", "dev:nile": "cross-env VITE_APP_ENV=nile vite", "dev:test": "cross-env VITE_APP_ENV=test vite", "dev:shasta": "cross-env VITE_APP_ENV=shasta vite", "dev:main": "cross-env vite", "build:nile": "tsc & cross-env VITE_APP_ENV=nile vite build", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tronweb3/abstract-adapter-evm": "^1.0.2", "@tronweb3/tronwallet-abstract-adapter": "^1.1.9", "@tronweb3/tronwallet-adapter-metamask": "^1.0.2", "@tronweb3/tronwallet-adapter-react-hooks": "^1.1.10", "@tronweb3/tronwallet-adapter-react-ui": "^1.1.9", "@tronweb3/tronwallet-adapter-tronlink-evm": "^1.0.1", "@tronweb3/tronwallet-adapters": "^1.2.9", "animate.css": "^4.1.1", "antd": "^5.21.5", "axios": "^1.8.4", "bignumber.js": "^9.1.2", "classnames": "^2.5.1", "cross-env": "^7.0.3", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "ethers": "^6.13.4", "lodash-es": "^4.17.21", "lottie-web": "^5.13.0", "mobx": "^6.13.5", "mobx-react-lite": "^4.0.7", "react": "^18.3.1", "react-dom": "^18.3.1", "react-intersection-observer": "^9.13.1", "react-intl-universal": "^2.11.3", "react-parallax-tilt": "^1.7.299", "react-router-dom": "^6.27.0", "tronweb": "^6.0.3", "vconsole": "^3.15.1"}, "devDependencies": {"@eslint/js": "^9.11.1", "@types/lodash-es": "^4.17.12", "@types/react": "^18.3.10", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.2", "css-loader": "^7.1.2", "eslint": "^9.11.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.12", "globals": "^15.9.0", "prettier": "^3.3.3", "sass-embedded": "^1.80.3", "style-loader": "^4.0.0", "typescript": "^5.5.3", "typescript-eslint": "^8.7.0", "vite": "^5.4.8", "vite-plugin-node-polyfills": "^0.22.0"}, "pnpm": {"overrides": {"ws@>=8.0.0 <8.17.1": ">=8.17.1", "esbuild@<=0.24.2": ">=0.25.0", "vite@>=5.0.0 <5.4.15": ">=5.4.15", "vite@>=5.0.0 <5.4.16": ">=5.4.16", "vite@>=5.0.0 <5.4.18": ">=5.4.18", "vite@>=5.0.0 <=5.4.18": ">=5.4.19", "vite@>=5.0.0 <5.4.17": ">=5.4.17", "brace-expansion@>=1.0.0 <=1.1.11": ">=1.1.12", "brace-expansion@>=2.0.0 <=2.0.1": ">=2.0.2", "pbkdf2@<=3.1.2": ">=3.1.3", "pbkdf2@>=3.0.10 <=3.1.2": ">=3.1.3"}}}