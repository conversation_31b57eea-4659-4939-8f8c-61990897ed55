import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { nodePolyfills } from 'vite-plugin-node-polyfills';
import path from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react(), nodePolyfills()],
  css: {
    preprocessorOptions: {
      scss: {
        api: 'modern-compiler'
      }
    }
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@components': path.resolve(__dirname, './src/components')
    }
  },
  server: {
    host: '0.0.0.0',
    proxy: {
      // 代理统计API请求
      '/stats-api': {
        target: 'https://open-test.gasfree.io',
        changeOrigin: true,
        rewrite: path => path.replace(/^\/stats-api/, '')
      },
      // 保留原有的API代理
      '/api/v1/': 'http://************:8080/'
    }
  }
});
